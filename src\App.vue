<script>
export default {
  onLaunch: function () {
    console.log('App Launch')

		// 初始化最后活动时间
		uni.setStorageSync("lastActiveTime", new Date().getTime());


    // #ifdef MP-WEIXIN
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function (res) {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          })
          updateManager.onUpdateFailed(function () {
            wx.showModal({
              title: '已经有新版本了',
              content: '新版本已经上线，请删除当前小程序，重新打开'
            })
          })
        }
      })
    }
    // #endif		
        
        // 添加全局点击事件监听
        uni.onUserCaptureScreen(() => {
            this.updateLastActiveTime();
        });
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },

  methods: {
        // ... 其他方法保持不变 ...
// 小程序自动更新
// if (wx.canIUse('getUpdateManager')) {
//   const updateManager = wx.getUpdateManager()
//   updateManager.onCheckForUpdate(function (res) {
//     // 请求完新版本信息的回调
//     if (res.hasUpdate) {
//       updateManager.onUpdateReady(function () {
//         wx.showModal({
//           title: '更新提示',
//           content: '新版本已经准备好，是否重启应用？',
//           success: function (res) {
//             if (res.confirm) {
//               // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
//               updateManager.applyUpdate()
//             }
//           }
//         })
//       })
//       updateManager.onUpdateFailed(function () {
//         // 新的版本下载失败
//         wx.showModal({
//           title: '已经有新版本了哟~',
//           content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
//         })
//       })
//     }
//   })
// }        
        updateLastActiveTime() {
            uni.setStorageSync("lastActiveTime", new Date().getTime());
        }
  },
  onPageNotFound: [
    	function (res) {
    		// 跳转到 404 页面：
      		uni.redirectTo({
        		url: "pages/Error/Error", // 404 页面的路径
      		});
    	},
  ],
}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "@/colorui/main.css";
	@import "@/colorui/icon.css";
	@import "@/colorui/animation.css";
	
	
	// 注册登录页引用
	@import "@/common/styles/qui_weex.scss";
	@import "@/pages/auth/styles/skin03.scss";
	
	// 微信||H5引用图标库
	//#ifdef H5 || MP-WEIXIN
	@import "@/common/font/iconfont.css";
	//#endif
	
	//APP引用图标库
	//#ifdef APP-PLUS
	@import "@/static/font/iconfont2.css";
	//#endif

</style>
