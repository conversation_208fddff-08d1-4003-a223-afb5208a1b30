    $boxWidth:640rpx;
    $mainColor: #ff7e00;
	.logoimg{
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
	}
	.placeholder{
		color: #DCDFE6;
	}
	.main-color{
		color: $mainColor;
	}
	.underline{
		text-decoration: underline;
	}
	.tabs{
		width: $boxWidth;
		height: 100rpx;
	}
	.btns{
		width: $boxWidth;
		margin-top: 0rpx;
		display: flex;
		flex-direction: column-reverse;
		align-items: center;
		justify-content: space-around;
	}
	.btns .flex{
		width: $boxWidth;
	}
	.qbtn{
		margin-top: 100rpx;
		height: 140rpx;
		width: 140rpx;
		border-radius: 70rpx;
		background-color: $mainColor;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
		box-shadow: 0 1px 0 2px rgba(0,0,0,0.05);
	}
	.btn-text-color{
		color: #ffffff;
	}
	.nav-text-color{
		color: #999999;
	}
	.label{
		width: 120rpx;
		display: none;
	}
	.label_icon{
		width: 38rpx;
		height: 38rpx;
		margin-right: 30rpx;
		display: block;
	}
	.label_fgs{
		height: 36rpx;
		width: 1px;
		border-left:1px solid #E5E5E5;
		margin-right: 30rpx;
		display: none;
	}
  .topbox{
	  width: 100%;
	  height: 400rpx;
  }
  .form{
	  width: $boxWidth;
  }
  .form-item{
	  padding: 20rpx 20px;
	  border-radius: 50rpx;
	  background-color: #ffffff;
	  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	  margin-bottom: 20px;
  }
  .active{
	  position: relative;
  }
  .active::after{
	  content: "";
	  position: absolute;
	  bottom:0;
	  width: 200rpx;
	  height: 4rpx;
	  border-radius: 4rpx;
	  background-color: $mainColor;
  }
  .curtext{
	  color: $mainColor;
  }