<template>
    <view class="login-box">
      <view class="main-box">
        <view>
          <!-- <img src="https://img.qumoyugo.com/webimgbg/pk_logo.png"  class="img" alt=""> -->
        </view>
        <view class="login-com">
          <button type="default" v-if="checked" @click="toLoginByWx" class="btn">
            微信登录
          </button>
          <view type="default" v-else @click="handlerTips" class="btn">
            微信登录
          </view>
          <button type="default" v-if="checked" open-type="getPhoneNumber" @getphonenumber="getphonenumber" class="btn">
            手机号快捷登录
          </button>
          <view type="default" v-else @click="handlerTips" class="btn">
            手机号快捷登录
          </view>
        </view>
      </view>
      <view class="bottom">
        <!-- <label class="radio">
          <radio style="transform:scale(0.7)" :checked="checked" color="#000000" />
        </label> -->
        <label class="radio" @click="handlerClickRadio">
          <uni-icons :type="checked ? 'checkbox-filled':'circle'" size="20"></uni-icons>
        </label>
        <view>
          <text @click="checked = !checked">我已阅读并同意</text>
          <text @click="gotoPage(1)" style="color:#000000">《用户协议》</text>和
          <text @click="gotoPage(2)" style="color:#000000">《隐私保护协议》</text>
        </view>
      </view>
    </view>
    <!-- <SelfToast content="请阅读并勾选下方《品客聚精彩用户协议》和《隐私保护协议》" ref="selfToast"></SelfToast> -->
    <view class="u-center-popupRef" v-if="popupRef">
      <view class="popup-toast">
        <view class="title">提示</view>
        <view class="">请阅读并勾选下方《用户协议》和《隐私保护协议》</view>
      </view>
    </view>
    <!-- <template v-if="PLATFORM !== 'prod'">
      <view style="padding: 54rpx">
        <uni-easyinput trim="all" v-model="telNumber" placeholder="输入手机号"></uni-easyinput>
        <button @click="btnLogin" style="margin-top:40rpx" type="primary">注册</button>
      </view>
    </template> -->
  </template>
  
  <script>

// import { useStore } from '@/store' // Options API 通常通过 this.$store 访问
// import { storage, urlGet } from '@/utils/utils'
// import { TOKEN, REGISTER_FORM_MP, REGISTER_CUSTOMER_ID, REGISTER_HELP_SUCCESS, OPEN_ADD_PETS } from '@/assets/js/config.js'
// import { loginIM } from '@/assets/js/tencentIM/tencentIM.js' // Keep commented if not used

import UniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';

const REGISTER_FORM_MP = 'registerfrom';
export default {
      // 添加 components 选项来注册导入的组件
  components: {
    UniEasyinput // 或者 'uni-easyinput': UniEasyinput
  },
  data() {
    return {
      // 使用 this.$config 或直接访问环境变量 (取决于您的项目配置方式)
      // 假设 $config.platform 存在，类似 index.vue 的 this.$config.baseApi
      // 如果没有，可以使用 import.meta.env
    //   PLATFORM: import.meta.env.VITE_APP_BASE_URL, // 或者 this.$config?.platform
        api: this.$config.baseApi,  
        checked: false,
    //   timer: null, // 注意：此 timer 在逻辑中未被使用
        timerLogin: null,
        popupRef: false,
        redirectUrl: '',
        telNumber: null, // 用于非生产环境测试输入

        // --- 从 appletsStore 移入的状态 ---
        pkId: "", // 初始化 pkId
        nickname: "点击登录", // 初始化 nickname
        headimgurl: "../../static/user/user.png", // 初始化 headimgurl
        // ---------------------------------

        // --- 可能需要的其他状态 (根据你的 Vuex 或全局状态管理) ---
        // userInfo: null, // 可以用来存储登录后的完整用户信息
        // token: null, // 可以用来存储登录令牌
        // -------------------------------------------------------


    }
  },
  // onLoad 生命周期钩子
  onLoad(option) {
    console.log('Login onLoad options:', option); // 调试信息
    if (option && option.redirect) { // 检查 option 是否存在
      try {
        // 解码可能存在的双重编码 URL
        let decoded = decodeURIComponent(option.redirect);
        // 检查是否需要再次解码
        if (decoded.includes('%')) {
            decoded = decodeURIComponent(decoded);
        }
        this.redirectUrl = decoded;
        console.log('解析后的 redirectUrl:', this.redirectUrl);
      } catch (e) {
        console.error('解码 redirect URL 失败:', e);
        this.redirectUrl = '/pages/index'; // 设置回退 URL
      }
    } else {
      this.redirectUrl = '/pages/index'; // 如果没有提供 redirect，设置默认值
    }
    console.log('最终 redirectUrl:', this.redirectUrl); // 确认最终值

    // --- 可以在这里尝试从本地存储恢复登录状态 ---
    // const storedUserInfo = uni.getStorageSync("appleteUserInfo"); // 或其他 key
    // if (storedUserInfo && storedUserInfo.pkId) {
    //   this.pkId = storedUserInfo.pkId;
    //   this.nickname = storedUserInfo.nickname;
    //   this.headimgurl = storedUserInfo.headimgurl;
    //   // 如果使用 token，也需要恢复
    //   // this.token = uni.getStorageSync(TOKEN);
    //   // 如果使用 Vuex，可能需要 commit
    //   // this.$store.commit('setUserInfo', storedUserInfo);
    //   // this.$store.commit('setToken', this.token);
    // }
    // ---------------------------------------------

  },
  methods: {
    gotoPage(value) {
      uni.navigateTo({
        url: value === 2 ? '/pages/privacy' : '/pages/concert'
      })
    },

    handlerClickRadio() {
      this.checked = !this.checked
    },

    handlerTips() {
      this.popupRef = true
      // 清除之前的定时器（如果存在）
      if (this.timerLogin) {
        clearTimeout(this.timerLogin)
      }
      this.timerLogin = setTimeout(() => {
        this.popupRef = false
      }, 2000)
    },


    toLoginByWx() {
        console.log("进入微信登录");
        const that = this;

        // 可以选择性地保留已登录检查，但现在检查 this.pkId 或 this.token
        if (that.pkId) { // 或者检查 token 是否有效
            console.log("用户已登录");
            uni.showToast({ title: '您已登录', icon: 'none' });
            return;
        }

        //已登录
        // if(appletsStore.state.pkId!==''){
	    //     return;
        // }
   	    // 获取微信服务商权限
	    // 在App平台，可用的服务商，是打包环境中配置的服务商，与手机端是否安装了该服务商的App没有关系。
   	    uni.getProvider({
   	 	    service: 'oauth',
   	 	    success: function (res) {
   	 		    console.log(res.provider)
   	 		    if (~res.provider.indexOf('weixin')) {
   				    //如果支持微信登录 进行登录
   	 			    uni.login({
   	 				    "provider": "weixin",
   	 				    "onlyAuthorize": true, // 微信登录仅请求授权认证
   	 				    success: function(loginRes){
                            console.log('uni.login (wx.login) success:', loginRes);
						    //客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
						    uni.request({
						        url: that.api + '/api/wxLogin/user/getUserLoginByApplets', //仅为示例，并非真实接口地址。
                                // method: 'POST', // 确认后端接口请求方法
                                // header: {'Content-Type': 'application/json'}, // 根据后端要求设置
						        data: {
						            code: loginRes.code
						        },
						        success: (res) => {
                                    const resData = res.data;
                                    console.log('Backend login response:', resData);
                                    uni.hideLoading();
						            //获得用户信息保存并提示完成登录
								    if(resData && resData.pkId){
                                        // 更新组件内部状态
                                        that.pkId = resData.pkId;
                                        that.nickname = resData.nickname;
                                        if (resData.headimgurl) { // 检查头像是否存在
                                            that.headimgurl = resData.headimgurl;
                                        } else {
                                            that.headimgurl = "../../static/user/user.png"; // 使用默认头像
                                        }
                                        // --- 更新本地存储 (存储整个对象或只存关键信息如 token) ---
                                        // 方式一：存储组合对象 (类似 appletsStore)
                                        uni.setStorageSync("appleteUserInfo", {
                                            pkId: that.pkId,
                                            nickname: that.nickname,
                                            headimgurl: that.headimgurl
                                        });
                                        // 方式二：如果后端返回 token，优先存储 token
                                        // if (resData.token) {
                                        //   uni.setStorageSync(TOKEN, resData.token);
                                        //   that.token = resData.token; // 更新组件状态
                                        //   // 如果需要，也可以存储用户信息
                                        //   uni.setStorageSync("userInfo", { nickname: that.nickname, headimgurl: that.headimgurl });
                                        // }

                                        // --- 如果使用 Vuex，提交 mutation ---
                                        // if (that.$store) {
                                        //   that.$store.commit('setUserInfo', { pkId: that.pkId, nickname: that.nickname, headimgurl: that.headimgurl });
                                        //   if (resData.token) {
                                        //      that.$store.commit('setToken', resData.token);
                                        //   }
                                        // }
                                        // ------------------------------------
                                        uni.showModal({
                                            title: "微信登录成功" ,
                                            icon: 'none'
                                        });	
								    }
						        }
						    });
   	 				    },
                        fail: function (err) {
                            // 登录授权失败  
                            // err.code是错误码
                            console.log(err);
                            uni.showToast({
                                title: err.errMsg ,
                                icon: 'none'
                            });
                        }
   	 			    })
   	 		    }
   	 	    },
            //请求失败
            fail: (err) => {
                console.log(err);
                uni.showToast({
                    title: "手机不支持微信登录" ,
                    icon: 'none'
                });
            },
   	    });
    },    

    getphonenumber(e) {
      console.log('getphonenumber event:', e); // 调试信息
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        console.warn('用户拒绝授权手机号:', e.detail.errMsg)
        // 可以选择性地给用户提示
        // uni.showToast({ title: '需要授权手机号才能登录', icon: 'none' });
        return false
      }
      // 确保 detail 中有 code
      if (!e.detail.code) {
        console.error('获取手机号 code 失败:', e.detail);
        uni.showModal({
          title: '提示',
          content: '获取手机号信息失败，请稍后重试或检查升级微信版本。',
          showCancel: false
        })
        return false
      }

      // 使用 wx.login 获取用户的登录 code 用于会话交换
      wx.login({
        success: (loginRes) => {
          console.log('wx.login success:', loginRes); // 调试信息
          if (!loginRes.code) {
            uni.showToast({ title: '获取登录凭证失败', icon: 'none' });
            return;
          }

          uni.showLoading({
            title: '登录中...'
          })
          const that = this; // 保存 this 上下文
          // 1. 交换 wx.login code 获取 openid/session_key
          uni.request({ // 使用 this.$https
            url: that.api + '/api/wxLogin/user/js-token',//'/js-token', // 后端交换 code 的端点
            // method: 'POST', // 或 GET，取决于您的后端
            // header: {'Content-Type': 'application/json'},
            data: {
              code: loginRes.code,
            //   scene: 'mp4' // 或您特定的场景标识符
            },
            success: (resOpenid) => {
                console.log('/js-token response:', resOpenid); // 调试信息
                const serverResponse = resOpenid.data;
                console.log('Code-to-OpenID endpoint response data:', serverResponse); // 调试实际的服务器响应
                if (resOpenid.statusCode === 200 && serverResponse && serverResponse.code === 0 && serverResponse.data && serverResponse.data.openid) {
                    const openId = resOpenid.data.openid;

                    // 准备使用手机号 code 和 openid 的登录数据 (使用 uni.getStorageSync/uni.removeStorageSync)
                    const registerFrom = uni.getStorageSync(REGISTER_FORM_MP);
                    // const recommendCustomerId = uni.getStorageSync(REGISTER_CUSTOMER_ID);
                    // uni.removeStorageSync(REGISTER_CUSTOMER_ID); // 获取后移除
                    uni.removeStorageSync(REGISTER_FORM_MP);    // 获取后移除

                    const loginData = {
                        code: e.detail.code, // 这是手机号的 code
                        type: 'wechat',
                        registerPhoneType: 'MP',
                        scene: 'mp4',
                        openId: openId, // 传递获取到的 openId
                        // sessionKey: sessionKey, // 仅当后端需要时传递
                        // recommendCustomerId: recommendCustomerId || null // 确保未定义时为 null
                    }

                    // 根据 redirectUrl 添加可选参数
                    const redirect = that.redirectUrl; // 使用局部变量以便清晰
                    // if (redirect.includes('/events/eventsDetail') && urlGet('share', redirect)) {
                    //     loginData.relationActivityId = urlGet('id', redirect)
                    // }
                    // const entryType = that.urlGet('entryType', that.redirect);
                    // if (entryType) {
                    //     loginData.entryType = entryType;
                    // }
                    if (registerFrom) {
                        loginData.registerFrom = registerFrom
                    }
                    // const relationActivityId = urlGet('relationActivityId', redirect);
                    // if (relationActivityId) {
                    //     loginData.relationActivityId = relationActivityId; // 如果两者都存在则覆盖之前的
                    // }
                    // const activityOwnerId = urlGet('activityOwnerId', redirect);
                    // if (activityOwnerId) {
                    //     loginData.activityOwnerId = activityOwnerId;
                    // }

                    console.log('Login data:', loginData) // 调试信息

                // 2. 调用实际的登录端点
                uni.request({
                    url: that.api + '/login',
                    //method: 'POST',
                    header: {'Content-Type': 'application/json'}, 
                    data: loginData,
                    success: (res) => {
                        const resData = res.data; 
                        console.log('/login response:', resData); // 调试信息
                        uni.hideLoading() // 隐藏加载指示器
                        if (resData.code === 0 && resData.data.token) {
                            // 登录成功
                            uni.removeStorageSync(REGISTER_FORM_MP); // 以防万一再次清理存储
                            that.$store.commit('setToken', resData.data.token); // Vuex 操作不变
                            uni.setStorageSync(TOKEN, resData.data.token); // 使用 uni.setStorageSync

                            if (entryType === '2') { // 再次检查 entryType 以执行特定逻辑
                                uni.setStorageSync(OPEN_ADD_PETS, true); // 使用 uni.setStorageSync
                            }

                            let targetUrl = '';
                            if (resData.data.hasCompleted) {
                                targetUrl = decodeURIComponent(redirect) // 使用原始重定向 URL
                            } else {
                                // 重定向到资料完善页面，传递原始重定向地址
                                targetUrl = `/user/uploadAvatar?redirect=${encodeURIComponent(redirect)}`
                            }
                            console.log('跳转url链接1', targetUrl);

                            // 处理 eventsDetail 重定向的特定逻辑
                            if (targetUrl.includes('/events/eventsDetail') && urlGet('share', targetUrl) && +urlGet('rmd', targetUrl) !== resData.data.customerId) {
                                uni.setStorageSync(REGISTER_HELP_SUCCESS, 1)
                            }

                            // 确保 targetUrl 有效且不是登录页面本身
                            if (!targetUrl || targetUrl.includes('/login') || targetUrl === '/') {
                                targetUrl = '/pages/index' // 默认为首页
                            }
                            console.log('跳转url链接2', targetUrl);

                            // 执行重定向
                            uni.redirectTo({
                                url: targetUrl,
                                fail: (err) => {
                                console.warn(`redirectTo ${targetUrl} 失败, 尝试 switchTab:`, err)
                                // 如果 redirectTo 失败（例如目标是 tab 页面），则回退到 switchTab
                                uni.switchTab({
                                    url: targetUrl,
                                    fail: (switchErr) => {
                                    console.error(`switchTab ${targetUrl} 也失败了:`, switchErr);
                                    // 如果 switchTab 也失败，则最终回退到首页
                                    uni.reLaunch({ url: '/pages/index' });
                                    }
                                })
                                }
                            })

                            // 登录成功并设置重定向后获取个人资料
                            uni.request({
                                url: that.api + '/profile',
                                method: 'GET', // 通常个人资料使用 GET
                                header: { 'Authorization': 'Bearer ' + resData.data.token }, // 假设需要 Token 认证
                                success: (profileRes) => {
                                    const profile = profileRes.data;
                                    console.log('/profile response:', profile); // 调试信息
                                    if (profile.code === 0) {
                                        this.$store.commit('setProfile', profile.data) // 使用 this.$store

                                    } else {
                                        console.warn('获取用户信息失败:', profile.message);
                                    }
                                },
                                fail: (profileErr) => {
                                    console.error('请求用户信息接口时出错:', profileErr);
                                },
                                complete: () => {
                                    uni.hideLoading()
                                }
                            });
                        } else {
                        // 登录失败
                        uni.hideLoading(); // 确保隐藏加载
                        uni.showModal({
                            title: '登录失败',
                            content: resData.message || '无法登录，请稍后重试。',
                            showCancel: false
                        })
                    }
                },
                fail:(loginErr) => {
                    uni.hideLoading()
                    console.error('登录请求失败:', loginErr)
                    uni.showModal({
                    title: '网络错误',
                    content: '登录请求失败，请检查网络连接。',
                    showCancel: false
                    })
                },
                complete: () => {
                        // 确保在登录请求完成后（无论成功或失败）隐藏 loading，除非在成功逻辑中已经处理
                        // if (resData.code !== 0) uni.hideLoading(); // 可以在这里加，但上面 success/fail 分别处理更精确
                }
            });

            } else {
                uni.hideLoading()
                console.error('换取 openid 失败:', resOpenid)
                uni.showModal({
                    title: '登录失败',
                    content: resOpenid.message || '无法获取用户信息凭证，请重试。',
                    showCancel: false
                })
            }
        },
        fail:(openidErr) => {
            uni.hideLoading()
            console.error('请求 /js-token 接口时出错:', openidErr)
            uni.showModal({
                title: '网络错误',
                content: '无法连接服务器进行登录准备，请检查网络。',
                showCancel: false
            })
        },
        complete: () => {
            
        }
        });
        },
        fail: (loginErr) => {
          uni.hideLoading(); // 确保失败时隐藏加载
          console.error('wx.login 调用失败:', loginErr);
          uni.showToast({ title: '微信登录接口调用失败', icon: 'none' });
        }
      })
    },
    urlGet(name, url) {
        // 如果 urlGet 不是全局混入的，可以在这里定义
        if (!url) url = window.location.href; // 或其他获取当前 URL 的方式
        url = url.split('#')[0]; // 获取查询字符串部分
        const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        const r = url.substring(url.indexOf("\?") + 1).match(reg);
        if (r != null) return decodeURIComponent(r[2]); return null;
    },
    // --- 非生产环境测试登录 ---
    btnLogin() {

      if (!this.telNumber) {
          uni.showToast({ title: '请输入手机号', icon: 'none' });
          return;
      }
      if (!/^\d{11}$/.test(this.telNumber)) {
          uni.showToast({ title: '请输入有效的11位手机号', icon: 'none' });
          return;
      }
      const that = this; // 保存 this 上下文
              // 确保 urlGet 函数可用
      const urlGet = that.urlGet || function(name, url) { /* 实现或导入 urlGet */ return null; }; 
    //   const recommendCustomerId = uni.getStorageSync(REGISTER_CUSTOMER_ID); // 使用 uni.getStorageSync
    //   uni.removeStorageSync(REGISTER_CUSTOMER_ID);

      const data = {
        account: `86-${this.telNumber}`, // 假设后端期望此格式
        code: this.PLATFORM === 'sprint' ? '7777' : '0832', // 测试码
        type: 'phone', // 登录类型
        registerPhoneType: 'MP', // 假设注册类型相同
        scene: 'mp4', // 假设场景相同
        // entryType: that.urlGet('entryType', that.redirectUrl),
        // relationActivityId: urlGet('relationActivityId', this.redirectUrl) || urlGet('id', this.redirectUrl),
        // activityOwnerId: urlGet('activityOwnerId', this.redirectUrl)
      }
    //   if (recommendCustomerId) {
    //     data.recommendCustomerId = recommendCustomerId
    //   }

      uni.showLoading({ title: '登录中...' });

      uni.request({
        url: that.api + '/login',
        method: 'POST',
        header: {'Content-Type': 'application/json'},
        data,
        success: (res) => {
            const resData = res.data;
            uni.hideLoading();
            if (resData.code === 0 && resData.data.token) {
                // 登录成功
                this.$store.commit('setToken', res.data.token);
                uni.setStorageSync(TOKEN, resData.data.token); // 使用 uni.setStorageSync
                uni.setStorageSync(OPEN_ADD_PETS, true); // 假设测试登录总是设置此项

                let targetUrl = '';
                const redirect = this.redirectUrl;
                if (res.data.hasCompleted) {
                    targetUrl = decodeURIComponent(redirect);
                } else {
                    targetUrl = `/user/uploadAvatar?redirect=${encodeURIComponent(redirect)}`;
                }
                console.log('跳转url链接1', targetUrl);

                if (targetUrl.includes('/events/eventsDetail') && urlGet('share', targetUrl) && +urlGet('rmd', targetUrl) !== res.data.customerId) {
                    uni.setStorageSync(REGISTER_HELP_SUCCESS, 1);
                }

                if (!targetUrl || targetUrl.includes('/login') || targetUrl === '/') {
                    targetUrl = '/pages/index';
                }
                console.log('跳转url链接2', targetUrl);

                uni.redirectTo({
                    url: targetUrl,
                    fail: () => {
                        uni.switchTab({
                            url: targetUrl,
                            fail: () => uni.reLaunch({ url: '/pages/index' })
                        })
                    }
                })

          // 获取个人资料
          uni.request({
            url: that.api + '/profile',
            method: 'GET',
            header: { 'Authorization': 'Bearer ' + resData.data.token }, // 假设需要 Token 认证
            success: (profileRes) => {
                const profile = profileRes.data;
                if (profile.code === 0) {
                    this.$store.commit('setProfile', profile.data)
                }
            },
            fail: (profileErr) => {
                 console.error('请求用户信息接口时出错:', profileErr);
            },
            complete: () => {
                uni.hideLoading(); // 个人资料获取完成后隐藏 loading
            }
          });

        } else {
            uni.hideLoading();
            uni.showModal({
                title: '登录失败',
                content: res.message || '测试登录失败，请检查。',
                showCancel: false
            })
        }
    },
    fail: (err) => {
          uni.hideLoading();
          console.error('测试登录请求失败:', err);
          uni.showModal({
            title: '网络错误',
            content: '测试登录请求失败，请检查网络。',
            showCancel: false
          })
        },
        complete: () => {
            // 如果请求失败或成功但未获取到 profile，也需要 hideLoading
            // if (resData.code !== 0) uni.hideLoading(); // 可以在这里加，但上面 success/fail 分别处理更精确
        }
      });
    }
  },
  // beforeUnmount 生命周期钩子 (Vue 3) 或 beforeDestroy (Vue 2)
  // uniapp 中两者皆可，但 beforeUnmount 更符合 Vue 3 习惯
  beforeUnmount() {
    // 清理定时器
    if (this.timer) { // 虽然 timer 未使用，但以防万一
        clearTimeout(this.timer);
        this.timer = null;
    }
    if (this.timerLogin) {
      clearTimeout(this.timerLogin)
      this.timerLogin = null
    }
  }
}
</script>

  
  <style scoped lang="scss">
  .login-box{
    padding: 64rpx 15rpx;
    height: 100vh;
    .main-box{
      .img{
        width: 718rpx;
        height: 546rpx;
      }
      .login-com{
        margin: 0 72rpx;
        .btn{
          display: flex;
          justify-content: center;
          align-items: center;
          height: 84rpx;
          border-radius: 42rpx;
          background: #58AA46;
          color: #ffffff;
          margin-top: 90rpx;
          font-size: 32rpx;
          &:active{
            opacity: 0.8;
          }
        }
      }
    }
    .bottom{
      color: #999999;
      display: flex;
      margin: 40rpx 60rpx;
      font-size: 26rpx;
    }
  }
  .u-center-popupRef{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .popup-toast{
    width: 520rpx;
    padding: 20rpx 50rpx;
    background: rgba(0,0,0,0.8);
    border-radius: 12rpx;
    text-align: center;
    font-size: 28rpx;
    color: #ffffff;
    .title{
      font-size: 30rpx;
      height: 42rpx;
      line-height: 42rpx;
      margin-bottom: 14rpx;
    }
  }
  </style>