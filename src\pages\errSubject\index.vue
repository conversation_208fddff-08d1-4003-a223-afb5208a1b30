<template>
	<view>
		<!-- #ifdef H5 -->
		<view style="position: fixed; top:80rpx; z-index: 100; width: 100%; height: 80rpx; background-color: #fff;">
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<view style="position: fixed; top: 0rpx; z-index: 100; width: 100%; height: 80rpx; background-color: #fff;">
		<!-- #endif -->
		
		<!-- <view style="position: fixed; top: 80rpx; z-index: 100; width: 100%; height: 70rpx; background-color: #fff;"> -->
			<view  >
				<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button"
					activeColor="#00aaff"></uni-segmented-control>
			</view>

			<view  id="top-box" class="cu-bar bg-white solid-bottom">
				<view class="action text-black">
					<text v-if="currentType==1">判断题</text>
					<text v-else-if="currentType==2">单选题</text>
					<text v-else-if="currentType==3">多选题</text>
					<text class="cuIcon-edit text-green padding-left"></text>
					<text>{{subjectIndex+1}}/{{subjectList.length}}</text>
					<text style="padding-left: 30px;" class="cuIcon-check text-green padding-left"></text>
					<text>{{userSuccess}}</text>
					<text style="padding-left: 30px;" class="cuIcon-close text-red padding-left"></text>
					<text>{{userError}}</text>
				</view>
				<view class="action">
					<button id='dtk' class="cu-btn bg-green shadow" @tap="showCardModal"
						data-target="modalCard">答题卡</button>
				</view>
			</view>
		</view>
		<view class="cu-modal" :class="modalCard=='modalCard'?'show':''" @tap="hideCardModal">
			<view class="cu-dialog" @tap.stop>

				<scroll-view class="page padding" :scroll-y=true :style="{'height':swiperHeight}">
					<view class="cu-bar solid-bottom">
						<view class="action">
							<text class="cuIcon-title text-red"></text>
							<text>答题卡</text>
							<text style="padding-left: 30px;" class="cuIcon-edit text-green padding-left"></text>
							<text>{{subjectIndex+1}}/{{subjectList.length}}</text>
							<text style="padding-left: 30px;" class="cuIcon-check text-green padding-left"></text>
							<text>{{userSuccess}}</text>
							<text style="padding-left: 30px;" class="cuIcon-close text-red padding-left"></text>
							<text>{{userError}}</text>
						</view>
					</view>
					<view class="grid col-5 ">
						<view class="margin-tb-sm text-center" v-for="(subject,index) in subjectList" :key="index">
							<!-- 	<button  class="cu-btn round" :class="[subject.userAnswer.length===0?'line-grey':'bg-green']"
								@click="AppointedSubject(index)">{{index+1}}</button> -->
							<button class="cu-btn round" :style="index==subjectIndex? 'border: #007AFF solid 3px;':''"
								:class="[subject.userAnswer.length===0?'line-grey':subject.userAnswer==subject.answer?'bg-green':'bg-red']"
								@click="AppointedSubject(index)">{{index+1}}</button>
						</view>
					</view>

				</scroll-view>
			</view>
		</view>
		<view class="cu-modal padding " style="z-index: 900;" v-if="modalError=='modalError'" :class="modalError=='modalError'?'show':''" @tap="hideErrorModal">
			<view class="cu-dialog bg-white" @tap.stop>

				<view class="cu-bar solid-bottom ">
					<view class="action">
						<text class="cuIcon-title text-red"></text>试题纠错
					</view>
				</view>

			<radio-group @change="errRadioChange" class="block">
				<view class="cu-list menu text-left">
					<view  style="width: 100%;"   class="cu-item cu-item-error" v-for="(error,index) in errorList">
						<radio  style="width: 9%;" :value="index+''" :checked="index == errForm.errOption"  :id="'error'+index"></radio>
					 <label  style="width: 90%;" :for="'error'+index">	<view class="title text-black margin-left">{{error}}</view></label>
					</view>
				</view>
			</radio-group>
			<view class="cu-bar solid-bottom ">
				<view class="action">
					<textarea style="border: 1px solid #ccc;padding: 10rpx;text-align: left;" v-model="errForm.errMsg" placeholder="请输入发现的问题" />
				</view>
			</view>

				<view class="padding flex flex-direction ">
					<button class="cu-btn bg-red margin-tb-sm lg" @click="SubmitError">提 交</button>
					<button class="cu-btn bg-blue margin-tb-sm lg margin-left"  @click="modalError=''">取消</button>
				</view>
			</view>
		</view>
		<form>
			<!-- {{ edit_4: Add v-if check for subjectList length before rendering swiper }} -->
			<swiper v-if="subjectList && subjectList.length > 0" style="margin-top: 170rpx;" :current="subjectIndex" class="swiper-box" @change="SwiperChange" :style="'height:' +heigth+'px;'">
			<!-- {{ /edit_4 }} -->
			<!-- <swiper style="margin-top: 170rpx;" :current="subjectIndex" class="swiper-box" @change="SwiperChange" :style="'height:' +heigth+'px;'"> -->
				<!-- <swiper-item v-for="(subject,index) in subjectList"> -->
				<swiper-item v-for="(subject,index) in subjectList" :key="subject.sId"> <!-- Use a unique key like sId -->
					<!-- {{ /edit_5 }} -->				
					<!-- {{ edit_5: Add v-if check for subject object }} -->
					<view :id="'swid'+index" v-if="index-subjectIndex>=-1&&index-subjectIndex<=1">

						<view class="cu-bar bg-white solid-bottom">
							<view class="action ">
								<text class="cuIcon-title text-red"></text>
								<!-- Use optional chaining or v-if for safety -->
								<block>{{subject.sQuestion || '题目加载中...'}}</block>
								<!-- <block>{{subject.sQuestion}}</block> -->
							</view>

						</view>
						
						<view class="horViewStyle">
							<!-- Check subject.sImg existence -->	
							<image lazy-load=true v-if="subject.sImg && subject.sImg.length>0" style="margin: 0 auto;"
								:src="api+subject.sImg" mode=""></image>							 						
							<!-- <image lazy-load=true v-if="(subject.sImg!=null && subject.sImg.length>0)" style="margin: 0 auto;"
								:src="api+subject.sImg" mode=""></image> -->
						</view>
						<view>
							<!-- {{ edit_6: Add v-if check for subject.sType before rendering radio/checkbox groups }} -->
							<radio-group class="block" @change="RadioboxChange"
								v-if="subject.sType && (subject.sType==1||subject.sType==2)">
							<!-- {{ /edit_6 }} -->
							<!-- ... radio options ... -->
							</radio-group>
							<!-- {{ edit_7: Add v-if check for subject.sType before rendering radio/checkbox groups }} -->
							<checkbox-group class="block" @change="CheckboxChange" v-else-if="subject.sType && subject.sType==3">
							<!-- {{ /edit_7 }} -->
								<!-- ... checkbox options ... -->
							</checkbox-group>
						</view>

						<!-- {{ edit_8: Add v-if check for subject before accessing properties }} -->
						<view v-if="subject && (subject.showAnswer || current==1)" class="margin-top solid-top">
						<!-- {{ /edit_8 }} -->
							<view class="cu-bar min-height30">
								<view v-if="subject.userAnswer && subject.userAnswer.length>0" class="action  text-grey">
									<text>您的答案：</text>
									<text class="solid-bottom  padding-left text-green">{{subject.userAnswer}}</text>
								</view>
							</view>
							<view class="cu-bar min-height30">
								<view class="action  text-grey">
									<text>正确答案：</text>
									<text class="solid-bottom  padding-left text-green">{{subject.answer}}</text>
								</view>
							</view>

							<view class="cu-bar cu-bar-title">
								<view class="action  text-grey">
									<text>解析：</text>
								</view>
							</view>
							<view class="text-content padding  text-grey"
								style="width:90%;display:inline-block;white-space: pre-wrap; word-wrap: break-word;height: auto;">
								<text>{{subject.sExplain}}</text>
							</view>
						</view>

					</view>
				</swiper-item>
			</swiper>
			<!-- {{ edit_9: Add message for empty list }} -->
			<view v-else style="text-align: center; padding-top: 200rpx; color: #888;">
				暂无题目数据
			</view>
			<!-- {{ /edit_9 }} -->
		</form>
		<!-- {{ edit_10: Add v-if check for subjectList length before rendering footer actions relying on subjectIndex }} -->
		<view v-if="subjectList && subjectList.length > 0" id="foot-box" class="cu-bar tabbar bg-white shadow foot">
		<!-- {{ /edit_10 }} -->
			<view class="action" @click="MoveSubject(-1)">
				<view class="cuIcon-cu-image">
					<text class="lg cuIcon-back text-gray"></text>
				</view>
				<view class="text-gray">上一题</view>
			</view>
			<view class="action" @click="MoveSubject(1)">
				<view class="cuIcon-cu-image">
					<text class="lg text-gray cuIcon-right"></text>
				</view>
				<view class="text-gray">下一题</view>
			</view>

			<!-- {{ edit_11: Add v-if check for subjectList[subjectIndex] before accessing userFavor }} -->
			<view class="action" @click="FavorSubject">
				<view class="cuIcon-cu-image">
					<text class="lg cuIcon-favor" :class="[subjectList[subjectIndex] && subjectList[subjectIndex].userFavor?'text-red':'text-gray']"></text>
				</view>
				<view :class="[subjectList[subjectIndex] && subjectList[subjectIndex].userFavor?'text-red':'text-gray']">收藏</view>
			</view>
			<!-- {{ /edit_11 }} -->

			<view class="action" @click="ShowAnswerChange">
				<view class="cuIcon-cu-image">
					<text class="lg text-gray cuIcon-question"></text>
				</view>
				<view class="text-gray">解答</view>
			</view>
			<view class="action" @tap="showErrorModal" data-target="modalError">
				<view class="cuIcon-cu-image">
					<text class="lg text-gray cuIcon-warn"></text>
				</view>
				<view class="text-gray ">纠错</view>
			</view>

		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				//#ifdef MP-WEIXIN
					// 为了解决微信小程序无法使用全局变量修改所以每个文件都设置了服务端地址
				// api: 'http://www.xzys-mtczy-test.com',
				// api: 'http://*************:8080',
				//#endif
				api: this.$config.baseApi,
				userFavor: false, //是否已收藏
				heigth:750,
				swHeigth:750,
				userError: 0, //用户错题数量
				userSuccess: 0, //用户答对题目数量
				currentType: 0, //当前题型
				subjectIndex: 0, //跳转索引
				autoShowAnswer: true, //答错是否显答案
				autoRadioNext: true, //判断题、单项题，自动移下一题
				swiperHeight: '800px', //
				skin:0,//答对是移除
				title: '我的错题',
				boxTmp: '', //多选选项缓存
				items: ["答题模式", "背题模式"], //上方选项(答题模式,背题模式)
				current: 0, //上方选项索引
				subjectList: [{
						'sId': 10,
						"sQuestion": "",
						"sImg": null,
						"sType": 1,
						"itemA": "A",
						"itemB": "B",
						"itemC": "C",
						"itemD": "D",
						"answer": "B",
						"userAnswer": "",
						"userFavor": false,
						"sExplain": ""
					},
					


				],
				errForm:{
					errOption:'0',
					errMsg:''
				},
				modalCard: null, //显示答题卡
				modalError: null, //纠错卡
				errorList: ['题目不完整', '答案不正确', '含有错别字', '图片不存在', '解析不完整', '其他错误']
			}
		},
		onReady() {

			var tempHeight = 800;
			var _me = this;
			uni.getSystemInfo({
				//获取手机屏幕高度信息，让swiper的高度和手机屏幕一样高                
				success: function(res) {
					                 
					tempHeight = res.windowHeight;


					// console.log("屏幕可用高度 " + tempHeight);
					// console.log("屏幕可用宽度 " + res.windowWidth);
					uni.createSelectorQuery().select("#top-box").fields({
						size: true,
						scrollOffset: true
					}, (data) => {
						tempHeight -= data.height;
						// console.log("减掉顶部后的高度 " + tempHeight);

						uni.createSelectorQuery().select("#foot-box").fields({
							size: true,
							scrollOffset: true
						}, (data) => {
							tempHeight -= data.height;
							// console.log("减掉底部后的高度 " + tempHeight);
							_me.swiperHeight = tempHeight + 'px';
							_me.swHeigth=tempHeight;
							// console.log("滑屏最后高度 " + _me.swiperHeight);
						}).exec();

					}).exec();
				}
			});

		},
		onLoad(option) {
			uni.showLoading({title: '加载中',mask:true});
			this.skin==option.skin;
			let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
			let curRoute = routes[routes.length - 1].route //获取当前页面路由
			let curParam = routes[routes.length - 1].options; //获取路由参数
			
			let that = this
			let km = option.km == 0 ? 1 : 4;

			//设置请求的参数
			let params={
					'sCar': option.cartype,//请求的车型
					'sKm': option.sKm,//请求的科目
					'state':null,
					'errList':null
				};
			//设置请求状态
			params.state='cuoti';		
			//获取存在缓存中的错题集
			
			if(option.state=='collection'){
				params.errList=uni.getStorageSync("userInfo").uCollection;
			}else if(option.state=='cuoti'){
				params.errList=uni.getStorageSync("userErrorSubjectList").toString();
			}
			uni.request({
				method:"POST",
				url:this.api+'/api/findSubjectByCarAndKm',
				data:params, 
				header:{'Content-Type': 'application/x-www-form-urlencoded'},
				success(res) {
					//页面存储获取到的错题
					console.log(res)

					// {{ edit_1: Add check for res.data and filter result }}
					if (res.data && Array.isArray(res.data)) {
						// Filter the data first
						const filteredList = res.data.filter((item) => {
							// Add checks for item validity if necessary, e.g., ensure item and item.chapter exist
							return item && item.sKm == option.sKm && item.chapter && item.chapter.chapterCar.indexOf(option.sCar) >= 0;
						});								

						// Assign to subjectList
						that.subjectList = filteredList;

						// Check if the list is not empty before accessing the first element
						if (that.subjectList.length > 0) {
							that.currentType = that.subjectList[0].sType; // Safe access now
							
							// Add user answer field only if list is not empty
							for (var i = 0; i < that.subjectList.length; i++) {
								// Ensure item exists before setting property
								if (that.subjectList[i]) {
									that.$set(that.subjectList[i], "showAnswer", false);
								}
							}

							// Process user collection only if list is not empty
							if (uni.getStorageSync("userInfo") && uni.getStorageSync("userInfo").uCollection != undefined) {
								let uCollection = uni.getStorageSync("userInfo").uCollection;
								let carr = uCollection.split(',');
								for (let i of carr) {
									for (let s of that.subjectList) {
										// Ensure s exists and has sId before comparing and setting userFavor
										if (s && s.sId == i) {
											that.$set(s, "userFavor", true); // Use $set for reactivity
											break;
										}
									}
								}
								// Ensure default userFavor is set for items not in collection
								for (let s of that.subjectList) {
									if (s && s.userFavor === undefined) {
										that.$set(s, "userFavor", false);
									}
								}
							} else {
								// Set default userFavor if no collection info
								for (let s of that.subjectList) {
									if (s && s.userFavor === undefined) {
										that.$set(s, "userFavor", false);
									}
								}
							}

							that.setHeigth(); // Calculate height after data is ready
						} else {
							// Handle the case where no subjects are found after filtering
							console.warn("No subjects found matching the criteria.");
							that.subjectList = []; // Ensure it's an empty array
							that.currentType = 0; // Reset current type or set a default
						}

					} else {
						// Handle cases where res.data is not as expected
						console.error("Invalid data received from API:", res);
						that.subjectList = []; // Reset to empty array
						that.currentType = 0;
					}
					// {{ /edit_1 }}

					uni.setNavigationBarTitle({
						title: that.title
					});
					
					// //添加用户显示答案字段 // Moved inside the check
					// for (var i = 0; i < that.subjectList.length; i++) {
					// 	that.$set(that.subjectList[i], "showAnswer", false);
					// }
					
					
					// /** // Moved inside the check
					//  * 遍历用户收藏
					//  */
					// if(uni.getStorageSync("userInfo").uCollection!=undefined){
					// 	let uCollection = uni.getStorageSync("userInfo").uCollection;
					// 	let carr = uCollection.split(',');
					// 	for(let i of carr){
					// 		for(let s of that.subjectList){
					// 			if(s.sId == i){
					// 				s.userFavor=true;
					// 				break;
					// 			}
					// 		}
					// 	}
					// }
					
					// that.setHeigth(); // Moved inside the check
					
					setTimeout(function () {uni.hideLoading();}, 100);
					uni.setNavigationBarTitle({
						title:option.state=='cuoti'?'我的错题':'我的收藏'
					})
				},
				// {{ edit_2: Add error handling for the request }}
				fail(err) {
					console.error("API request failed:", err);
					that.subjectList = []; // Reset on failure
					that.currentType = 0;
					uni.hideLoading();
					uni.showToast({ title: '加载失败，请稍后重试', icon: 'none' });
				}
				// {{ /edit_2 }}
			})					
			

		},
		methods: {
			errRadioChange(e){
					
				  for (let i = 0; i < this.errorList.length; i++) {
						if (i == e.detail.value) {
							this.errForm.errOption = i+'';
							break;
						}
					}
			},
			setHeigth(){
				let that=this;
				
				uni.createSelectorQuery().select("#swid"+ that.subjectIndex).fields({
					size: true,
					scrollOffset: true
				}, (data) => {
					console.log(data);
					that.heigth=that.swHeigth>data.height?that.swHeigth:data.height+60;
					
				}).exec();
				
			},
			updateUserCollection(){
				uni.request({
					method: "POST",
					url: this.api + '/api/updateUserCollection',
					data: {
						uId:uni.getStorageSync("userInfo").uId,
						uCollection:uni.getStorageSync("userInfo").uCollection
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: (res) => {
						console.log(res);
					}
				})
			},
			
			showCardModal: function(e) {
				this.modalCard = e.currentTarget.dataset.target
			},
			hideCardModal: function(e) {
				this.modalCard = null
			},
			showErrorModal: function(e) {
				this.modalError = e.currentTarget.dataset.target
			},
			hideErrorModal: function(e) {
				this.modalError = null
			},
			SwiperChange: function(e) { //滑动事件

				let index = e.target.current;

				if (index != undefined) {
					this.subjectIndex = index;
					this.currentType = this.subjectList[index].sType;
					this.userFavor = this.subjectList[index].userFavor;
				}
				this.setHeigth();
			},
			RadioboxChange: function(e) { //单选选中

				console.log("这道题的SId", this.subjectList[this.subjectIndex].sId);
				let sId=this.subjectList[this.subjectIndex].sId;
				let that = this;
				
			
				var items = this.subjectList[this.subjectIndex].optionList;
				var values = e.detail.value;

				if (e.detail.value == this.subjectList[this.subjectIndex].answer) {
					if(uni.getStorageSync("userErrorSubjectSkin")){
					let userErrorSubjectList=uni.getStorageSync("userErrorSubjectList");
					 this.removeByValue(userErrorSubjectList,this.subjectList[this.subjectIndex].sId);
					uni.setStorageSync("userErrorSubjectList",userErrorSubjectList);
					}
					this.userSuccess++;
					this.subjectList[this.subjectIndex].userAnswer = values;
					this.subjectList[this.subjectIndex].showAnswer = true;
					if (this.autoRadioNext && this.subjectIndex < this.subjectList.length - 1) {
						this.subjectIndex += 1;
					};

				} else {
					// uni.removeStorageSync('userErrorSubjectList')
					 let userErrorSubjectList = uni.getStorageSync('userErrorSubjectList').length<=0?[]:uni.getStorageSync('userErrorSubjectList');
				
					 // 用户答错题判断缓存用户错题里存不存在这道题,不存在就添加
					if(userErrorSubjectList.indexOf(this.subjectList[this.subjectIndex].sId)==-1){
					userErrorSubjectList.push(this.subjectList[this.subjectIndex].sId);
					 uni.setStorageSync('userErrorSubjectList', userErrorSubjectList);
					 }
					 // }
					this.userError++;
					this.subjectList[this.subjectIndex].userAnswer = values;
					this.subjectList[this.subjectIndex].showAnswer = true;
				}
				// this.subjectList[this.subjectIndex].userAnswer = values;
				// if (this.autoRadioNext && this.subjectIndex < this.subjectList.length - 1) {
				// 	this.subjectIndex += 1;
				// };


			},
			CheckboxChange: function(e) { //复选选中
				var values = e.detail.value;
				this.boxTmp = e.detail.value.toString();

			},
			fxqd: function() { //复选确定按钮

				if (this.boxTmp.length > 0) {

					let sId=this.subjectList[this.subjectIndex].sId;
					this.subjectList[this.subjectIndex].userAnswer = this.boxTmp.replace(/,/g,'');
					this.boxTmp = '';

					console.log(this.subjectList[this.subjectIndex].userAnswer)
					//用户选对了答案
					if (this.subjectList[this.subjectIndex].userAnswer == this.subjectList[this.subjectIndex].answer) {
						if(uni.getStorageSync("userErrorSubjectSkin")){
							let userErrorSubjectList=uni.getStorageSync("userErrorSubjectList");
							 this.removeByValue(userErrorSubjectList,this.subjectList[this.subjectIndex].sId);
							uni.setStorageSync("userErrorSubjectList",userErrorSubjectList);
						}
						this.userSuccess++;
						this.subjectList[this.subjectIndex].showAnswer = true;
						if (this.autoRadioNext && this.subjectIndex < this.subjectList.length - 1) {
							this.subjectIndex += 1;
						};
					} else {
					let userErrorSubjectList = uni.getStorageSync('userErrorSubjectList').length<=0?[]:uni.getStorageSync('userErrorSubjectList');
									
					 // 用户答错题判断缓存用户错题里存不存在这道题,不存在就添加
					if(userErrorSubjectList.indexOf(this.subjectList[this.subjectIndex].sId)==-1){
					userErrorSubjectList.push(this.subjectList[this.subjectIndex].sId);
					 uni.setStorageSync('userErrorSubjectList', userErrorSubjectList);
					 }
						this.userError++;
						this.subjectList[this.subjectIndex].showAnswer = true;
					}
				} else {
					uni.showToast({
						title: this.windowWidth,
						icon: 'none',
						duration: 1000
					})
					uni.showToast({
						title: "您还没有任何选择",
						icon: 'none',
						duration: 1000
					})
				}

			},

			ShowAnswerChange: function(e) { //显示答案

				// console.log(this.subjectList[this.subjectIndex].showAnswer);

				if (this.subjectList[this.subjectIndex].showAnswer) {
					this.subjectList[this.subjectIndex].showAnswer = false;
					this.heigth-=320;
				} else {

					this.subjectList[this.subjectIndex].showAnswer = true;
					this.heigth+=320;
				}
				this.$forceUpdate();
			},

			FavorSubject: function(e) { //收藏题

				// {{ edit_3: Add check before accessing subjectList[subjectIndex] }}
				if (!this.subjectList || this.subjectIndex < 0 || this.subjectIndex >= this.subjectList.length || !this.subjectList[this.subjectIndex]) {
					console.error("Cannot favor subject: Invalid subject index or list.");
					return; // Prevent execution if index is invalid or subject is missing
				}
				// {{ /edit_3 }}

				/**
				 * 判断登录
				 */
				if(uni.getStorageSync("userInfo")==''){

					uni.showToast({
						title:"需要登录",
						icon:"error"
					})
					return;
				}
				
				if (this.subjectList[this.subjectIndex].userFavor) {
					this.$set(this.subjectList[this.subjectIndex], 'userFavor', false); // Use $set					
					
					//this.subjectList[this.subjectIndex].userFavor = false;
					let userInfo = uni.getStorageSync("userInfo");
					if (userInfo && userInfo.uCollection) { // Check if userInfo and uCollection exist
						let carr = userInfo.uCollection.split(',');
						this.removeByVal(carr,this.subjectList[this.subjectIndex].sId);//删除Sid
						userInfo.uCollection=carr.toString();
						uni.setStorageSync("userInfo",userInfo);
						// uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存 // Consider if this cache update is necessary here
						this.updateUserCollection();
					}
					// let carr = userInfo.uCollection.split(',');
					// this.removeByVal(carr,this.subjectList[this.subjectIndex].sId);//删除Sid
					// userInfo.uCollection=carr.toString();
					// uni.setStorageSync("userInfo",userInfo);
					// uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存
					// this.updateUserCollection();
				} else {
					this.$set(this.subjectList[this.subjectIndex], 'userFavor', true); // Use $set
					let userInfo = uni.getStorageSync("userInfo");
					if (userInfo) { // Check if userInfo exists
						// Initialize uCollection if it doesn't exist or is empty
						if (!userInfo.uCollection) {
							userInfo.uCollection = '';
						}
						// Avoid adding leading comma if collection is empty
						userInfo.uCollection = userInfo.uCollection ? userInfo.uCollection + ',' + this.subjectList[this.subjectIndex].sId : String(this.subjectList[this.subjectIndex].sId);
						uni.setStorageSync("userInfo",userInfo);
						// uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存 // Consider if this cache update is necessary here
						this.updateUserCollection();
					}

					// let userInfo = uni.getStorageSync("userInfo");
					// userInfo.uCollection+=','+this.subjectList[this.subjectIndex].sId;
					// uni.setStorageSync("userInfo",userInfo);
					
					// this.subjectList[this.subjectIndex].userFavor = true;
					// uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存
					// this.updateUserCollection();
				}
				
				
				// if (this.userFavor) {
				// 	this.userFavor = false;
				// 	this.subjectList[this.subjectIndex].userFavor = false;
				// } else {

				// 	this.userFavor = true;
				// 	this.subjectList[this.subjectIndex].userFavor = true;
				// }
			},

			MoveSubject: function(e) { //上一题、下一题

				if (e === -1 && this.subjectIndex != 0) {
					this.subjectIndex -= 1;
				}
				if (e === 1 && this.subjectIndex < this.subjectList.length - 1) {
					this.subjectIndex += 1;
				}
			},

			AppointedSubject: function(e) { //题卡指定

				this.modalCard = null;
				this.subjectIndex = e;
			},

			SubmitError: function(e) { //提交纠错

				if(uni.getStorageSync("userInfo")==''){
				
					uni.showToast({
						title:"需要登录",
						icon:"error"
					})
					return;
				}
				
				if(this.errForm.errMsg.length<5){
					uni.showModal({
						content:"至少输入5个字符的描述",
						showCancel:false
					})
					return;
				}else{
					let sId=Number.parseInt(this.subjectList[this.subjectIndex].sId);
					let uId=Number.parseInt( uni.getStorageSync("userInfo").uId);
					let str=this.errorList[Number.parseInt(this.errForm.errOption)]+',';
					uni.request({
						url:this.api+'/api/postErrorSubjectFB',
						method:"POST",
						data:{
							sId:sId,
							uId:uId,
							errMsg:str+this.errForm.errMsg
						},
						header:{
							'content-type': 'application/x-www-form-urlencoded',
						},
						success: (res) => {
							
							uni.showToast({
								icon:"success",
								title:res.data=='success'?'提交成功':'错误'
							})
							this.errForm.errMsg='';
							
						}
					})
					
					
				this.modalError = null;
				
				}
			},
			getWidth: function() { //返回屏幕宽度-60
				var w;
				uni.getSystemInfo({
					success(res) {
						w = res.windowWidth;
					}
				})
				return w - 60;
			},
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex;
				}
			},
		 removeByValue(arr, val) {//删除数组元素通过value
					for(var i = 0; i < arr.length; i++) {
						if(arr[i] == val) {
								arr.splice(i, 1);
								break;
							}
						}
					}
		},
		computed: {

		},
		onBackPress(e) {//监听用户返回前一页
			let pages = getCurrentPages(); 
			
			uni.redirectTo({
			    url: pages[1].route,
				success() {
					pages[1].init(pages[1].options)
				},
				fail() {
					pages[1].init(pages[1].options)
				}
			});
			//console.log(pages);
		},

	}
</script>

<style>
	@import "../../colorui/animation.css";

	page {
		background-color: #FFFFFF;
	}

	.cu-form-group {
		justify-content: flex-start
	}

	.cu-form-group .title {
		padding-left: 30upx;
		padding-right: 0upx;
	}

	.cu-form-group+.cu-form-group {
		border-top: none;
	}

	.cu-bar-title {
		min-height: 50upx;
	}

	.cu-list.menu>.cu-item-error {
		justify-content: flex-start;
	}

	.horViewStyle {
		display: flex;
		flex-direction: row;
		/* margin: 3rpx 2%; */
		align-content: center;
		align-items: center;
		width: 100%;

	}

	.min-height30 {
		min-height: 30px;
	}

	.margin-r-l {
		margin: 0 10px;
	}
</style>
