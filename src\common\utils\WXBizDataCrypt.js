// var crypto = require('crypto')
import CryptoJS from 'crypto-js'

function WXBizDataCrypt(appId, sessionKey) {
  this.appId = appId
  this.sessionKey = sessionKey
}

WXBizDataCrypt.prototype.decryptData = function (encryptedData, iv) {
  // base64 decode
  const sessionKey = CryptoJS.enc.Base64.parse(this.sessionKey)
  const encryptedDataBytes = CryptoJS.enc.Base64.parse(encryptedData)
  const ivBytes = CryptoJS.enc.Base64.parse(iv)

  try {
    // 解密
    const decrypt = CryptoJS.AES.decrypt(
      { ciphertext: encryptedDataBytes },
      sessionKey,
      {
        iv: ivBytes,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )

    const decoded = JSON.parse(decrypt.toString(CryptoJS.enc.Utf8))

    if (decoded.watermark.appid !== this.appId) {
      throw new Error('Illegal Buffer')
    }

    return decoded
  } catch (err) {
    throw new Error('Illegal Buffer')
  }
}

export default WXBizDataCrypt
