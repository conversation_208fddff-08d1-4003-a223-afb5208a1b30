<template>
	<view class="exam-container">
		<!-- 顶部固定区域 -->
		<!-- #ifdef H5 -->
		<view class="fixed-top" style="top:80rpx;">
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<view class="fixed-top" style="top:0rpx;">
		<!-- #endif -->
			<!-- 倒计时进度条 -->
			<view class="countdown-bar">
				<view class="countdown-time">{{djs}}</view>
				<view class="cu-progress">
					<view class="bg-red" :style="[{ width:time/startTime*100+'%'}]"></view>
				</view>
			</view>

			<!-- 题目信息栏 -->
			<view id="top-box" class="cu-bar bg-white solid-bottom">
				<view class="action text-black">
					<text v-if="subjectList[subjectIndex].sType==1" class="question-type">单选题</text>
					<text v-else-if="subjectList[subjectIndex].sType==2" class="question-type">判断题</text>
					<text v-else-if="subjectList[subjectIndex].sType==3" class="question-type">多选题</text>
					<text class="cuIcon-edit text-green padding-left"></text>
					<text>{{subjectIndex+1}}/{{subjectList.length}}</text>
					<text class="cuIcon-check text-green padding-left-sm"></text>
					<text>{{userSuccess}}</text>
					<text class="cuIcon-close text-red padding-left-sm"></text>
					<text>{{userError}}</text>
				</view>
				<view class="action">
					<!-- 添加语音播放按钮 -->
					<button class="cu-btn bg-blue shadow margin-right-sm" v-if="subjectList[subjectIndex] && subjectList[subjectIndex].sAudio && subjectList[subjectIndex].sAudio.length>0" @click="toggleAudio">
						<text class="lg" :class="[isPlaying ? 'cuIcon-stop' : 'cuIcon-voice']"></text>
						<!-- 语音播放按钮不显示文字 -->
						<!-- {{ isPlaying ? '暂停' : '语音' }} -->
					</button>
					<button id='dtk' class="cu-btn bg-green shadow" @tap="showCardModal" data-target="modalCard">答题卡</button>
				</view>
			</view>
		</view>

		<!-- 答题卡模态框 -->
		<view class="cu-modal" :class="modalCard=='modalCard'?'show':''" @tap="hideCardModal">
			<view class="cu-dialog" @tap.stop>
				<scroll-view class="page padding" :scroll-y=true :style="{'height':swiperHeight}">
					<view class="cu-bar solid-bottom">
						<view class="action">
							<text class="cuIcon-title text-red"></text>
							<text>答题卡</text>
							<text class="cuIcon-edit text-green padding-left-sm"></text>
							<text>{{subjectIndex+1}}/{{subjectList.length}}</text>
							<text class="cuIcon-check text-green padding-left-sm"></text>
							<text>{{userSuccess}}</text>
							<text class="cuIcon-close text-red padding-left-sm"></text>
							<text>{{userError}}</text>
						</view>
					</view>
					<view class="grid col-5">
						<view class="margin-tb-sm text-center" v-for="(subject,index) in subjectList" :key="index">
							<button class="cu-btn round"
								:style="index==subjectIndex? 'border: #007AFF solid 3px;':''"
								:class="[subject.userAnswer.length===0?'line-grey':subject.userAnswer==subject.answer?'bg-green':'bg-red']"
								@click="AppointedSubject(index)">{{index+1}}</button>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 纠错模态框 -->
		<view class="cu-modal padding" v-if="modalError=='modalError'" style="z-index: 900;" :class="modalError=='modalError'?'show':''" @tap="hideErrorModal">
			<view class="cu-dialog bg-white" @tap.stop>
				<view class="cu-bar solid-bottom">
					<view class="action">
						<text class="cuIcon-title text-red"></text>试题纠错
					</view>
				</view>

				<radio-group @change="errRadioChange" class="block">
					<view class="cu-list menu text-left">
						<view class="cu-item cu-item-error" v-for="(error,index) in errorList" :key="index">
							<radio style="width: 9%;" :value="index+''" :checked="index == errForm.errOption" :id="'error'+index"></radio>
							<label style="width: 90%;" :for="'error'+index">
								<view class="title text-black margin-left">{{error}}</view>
							</label>
						</view>
					</view>
				</radio-group>
				
				<view class="cu-bar solid-bottom">
					<view class="action" style="width: 100%;">
						<textarea class="error-textarea" v-model="errForm.errMsg" placeholder="请输入发现的问题" />
					</view>
				</view>

				<view class="padding flex">
					<button class="cu-btn bg-red margin-tb-sm lg flex-sub" @click="SubmitError">提 交</button>
					<button class="cu-btn bg-blue margin-tb-sm lg flex-sub margin-left" @click="modalError=''">取消</button>
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<form>
			<swiper class="swiper-box" :current="subjectIndex" @change="SwiperChange" :style="'height:' +heigth+'px;'">
				<swiper-item v-for="(subject,index) in subjectList" :key="index">
					<view :id="'swid'+index" v-if="index-subjectIndex>=-1&&index-subjectIndex<=1" class="question-container">
						<!-- 题目 -->
						<view class="question-title">
							<text class="cuIcon-title text-red"></text>
							<text class="question-text">{{subject.sQuestion}}</text>
						</view>

						<!-- 图片 -->
						<view class="image-container" v-if="subject.sImg && subject.sImg.length>0">
							<image lazy-load=true :src="api+subject.sImg" mode="widthFix" class="question-image"></image>
						</view>

						<!-- 选项区域 -->
						<view class="options-container">
							<!-- 单选/判断题 -->
							<radio-group class="block" @change="RadioboxChange" v-if="subject.sType==1||subject.sType==2">
								<!-- A选项 -->
								<label class="option-item">
									<view class="option-icon">
										<uni-icons v-if="subject.userAnswer.length>0"
											:type="subject.userAnswer=='A'?(subject.userAnswer==subject.answer?'checkbox-filled':'clear'):(subject.answer=='A'?'checkbox-filled':'circle')"
											:color="subject.userAnswer=='A'?(subject.userAnswer==subject.answer?'#00e54c':'#f41b31'):(subject.answer=='A'?'#00e54c':'#cccccc')"
											size="30">
										</uni-icons>
										<radio v-if="subject.userAnswer.length<=0"
											:value="'A'" :disabled="(subject.userAnswer.length>0)?true:false"
											:checked="subject.userAnswer.indexOf('A') > -1?true:false">
										</radio>
									</view>
									<view class="option-content">
										<text v-if="subject.sType==2">Y:འགྲིག</text>
										<text v-else>A.{{subject.itemA}}</text>
									</view>
								</label>

								<!-- B选项 -->
								<label class="option-item">
									<view class="option-icon">
										<uni-icons v-if="subject.userAnswer.length>0"
											:type="subject.userAnswer=='B'?(subject.userAnswer==subject.answer?'checkbox-filled':'clear'):(subject.answer=='B'?'checkbox-filled':'circle')"
											:color="subject.userAnswer=='B'?(subject.userAnswer==subject.answer?'#00e54c':'#f41b31'):(subject.answer=='B'?'#00e54c':'#cccccc')"
											size="30">
										</uni-icons>
										<radio v-if="subject.userAnswer.length<=0"
											:value="'B'" :disabled="(subject.userAnswer.length>0)?true:false"
											:checked="subject.userAnswer.indexOf('B') > -1?true:false">
										</radio>
									</view>
									<view class="option-content">
										<text v-if="subject.sType==2">N:ནོར།</text>
										<text v-else>B.{{subject.itemB}}</text>
									</view>
								</label>

								<!-- C选项 (仅单选题) -->
								<label v-if="subject.sType==1" class="option-item">
									<view class="option-icon">
										<uni-icons v-if="subject.userAnswer.length>0"
											:type="subject.userAnswer=='C'?(subject.userAnswer==subject.answer?'checkbox-filled':'clear'):(subject.answer=='C'?'checkbox-filled':'circle')"
											:color="subject.userAnswer=='C'?(subject.userAnswer==subject.answer?'#00e54c':'#f41b31'):(subject.answer=='C'?'#00e54c':'#cccccc')"
											size="30">
										</uni-icons>
										<radio v-if="subject.userAnswer.length<=0"
											:value="'C'" :disabled="(subject.userAnswer.length>0)?true:false"
											:checked="subject.userAnswer.indexOf('C') > -1?true:false">
										</radio>
									</view>
									<view class="option-content">
										<text>C.{{subject.itemC}}</text>
									</view>
								</label>

								<!-- D选项 (仅单选题) -->
								<label v-if="subject.sType==1" class="option-item">
									<view class="option-icon">
										<uni-icons v-if="subject.userAnswer.length>0"
											:type="subject.userAnswer=='D'?(subject.userAnswer==subject.answer?'checkbox-filled':'clear'):(subject.answer=='D'?'checkbox-filled':'circle')"
											:color="subject.userAnswer=='D'?(subject.userAnswer==subject.answer?'#00e54c':'#f41b31'):(subject.answer=='D'?'#00e54c':'#cccccc')"
											size="30">
										</uni-icons>
										<radio v-if="subject.userAnswer.length<=0"
											:value="'D'" :disabled="(subject.userAnswer.length>0)?true:false"
											:checked="subject.userAnswer.indexOf('D') > -1?true:false">
										</radio>
									</view>
									<view class="option-content">
										<text>D.{{subject.itemD}}</text>
									</view>
								</label>
							</radio-group>

							<!-- 多选题 -->
							<checkbox-group class="block" @change="CheckboxChange" v-else-if="subject.sType==3">
								<!-- A选项 -->
								<view class="option-item">
									<label>
										<view class="option-icon">
											<uni-icons v-if="subject.userAnswer.length>0"
												:type="subject.answer.indexOf('A')>-1?(subject.userAnswer.indexOf('A')>-1?'checkbox-filled':'circle'):(subject.userAnswer.indexOf('A')>-1?'clear':'clear')"
												:color="subject.answer.indexOf('A')>-1?(subject.userAnswer.indexOf('A')>-1?'#00e54c':'#00e54c'):(subject.userAnswer.indexOf('A')>-1?'#f41b31':'#cccccc')"
												size="25">
											</uni-icons>
											<checkbox v-if="subject.userAnswer.length<=0" :value="'A'"
												:class="boxTmp.indexOf('A') > -1?'checked':''"
												:checked="boxTmp.indexOf('A') > -1?true:false">
											</checkbox>
										</view>
										<view class="option-content">
											A.{{subject.itemA}}
										</view>
									</label>
								</view>

								<!-- B选项 -->
								<view class="option-item">
									<label>
										<view class="option-icon">
											<uni-icons v-if="subject.userAnswer.length>0"
												:type="subject.answer.indexOf('B')>-1?(subject.userAnswer.indexOf('B')>-1?'checkbox-filled':'circle'):(subject.userAnswer.indexOf('B')>-1?'clear':'clear')"
												:color="subject.answer.indexOf('B')>-1?(subject.userAnswer.indexOf('B')>-1?'#00e54c':'#00e54c'):(subject.userAnswer.indexOf('B')>-1?'#f41b31':'#cccccc')"
												size="25">
											</uni-icons>
											<checkbox v-if="subject.userAnswer.length<=0" :value="'B'"
												:class="boxTmp.indexOf('B') > -1?'checked':''"
												:checked="boxTmp.indexOf('B') > -1?true:false">
											</checkbox>
										</view>
										<view class="option-content">
											B.{{subject.itemB}}
										</view>
									</label>
								</view>

								<!-- C选项 -->
								<view class="option-item">
									<label>
										<view class="option-icon">
											<uni-icons v-if="subject.userAnswer.length>0"
												:type="subject.answer.indexOf('C')>-1?(subject.userAnswer.indexOf('C')>-1?'checkbox-filled':'circle'):(subject.userAnswer.indexOf('C')>-1?'clear':'clear')"
												:color="subject.answer.indexOf('C')>-1?(subject.userAnswer.indexOf('C')>-1?'#00e54c':'#00e54c'):(subject.userAnswer.indexOf('C')>-1?'#f41b31':'#cccccc')"
												size="25">
											</uni-icons>
											<checkbox v-if="subject.userAnswer.length<=0" :value="'C'"
												:class="boxTmp.indexOf('C') > -1?'checked':''"
												:checked="boxTmp.indexOf('C') > -1?true:false">
											</checkbox>
										</view>
										<view class="option-content">
											C.{{subject.itemC}}
										</view>
									</label>
								</view>

								<!-- D选项 -->
								<view class="option-item">
									<label>
										<view class="option-icon">
											<uni-icons v-if="subject.userAnswer.length>0"
												:type="subject.answer.indexOf('D')>-1?(subject.userAnswer.indexOf('D')>-1?'checkbox-filled':'circle'):(subject.userAnswer.indexOf('D')>-1?'clear':'clear')"
												:color="subject.answer.indexOf('D')>-1?(subject.userAnswer.indexOf('D')>-1?'#00e54c':'#00e54c'):(subject.userAnswer.indexOf('D')>-1?'#f41b31':'#cccccc')"
												size="25">
											</uni-icons>
											<checkbox v-if="subject.userAnswer.length<=0" :value="'D'"
												:class="boxTmp.indexOf('D') > -1?'checked':''"
												:checked="boxTmp.indexOf('D') > -1?true:false">
											</checkbox>
										</view>
										<view class="option-content">
											D.{{subject.itemD}}
										</view>
									</label>
								</view>

								<button class="cu-btn bg-green margin-top" v-if="subject.userAnswer.length<=0" @click="fxqd">确定</button>
							</checkbox-group>
						</view>

						<!-- 答案解析 -->
						<view :id="'content'+index" v-show="subject.showAnswer" class="answer-explanation">
							<view class="explanation-item">
								<text class="explanation-label">您的答案：</text>
								<text class="explanation-content text-green">
									<template v-if="subject.sType==2">
										{{ subject.userAnswer === 'A' ? 'Y:འགྲིག' : (subject.userAnswer === 'B' ? 'N:ནོར།' : subject.userAnswer) }}
									</template>
									<template v-else>
										{{ subject.userAnswer }}
									</template>
								</text>
							</view>
							<view class="explanation-item">
								<text class="explanation-label">正确答案：</text>
								<text class="explanation-content text-green">
									<template v-if="subject.sType==2">
										{{ subject.answer === 'A' ? 'Y:འགྲིག' : (subject.answer === 'B' ? 'N:ནོར།' : subject.answer) }}
									</template>
									<template v-else>
										{{ subject.answer }}
									</template>
								</text>
							</view>
							<view class="explanation-item">
								<text class="explanation-label">解析：</text>
								<text class="explanation-content">{{subject.sExplain ? subject.sExplain : "无解析"}}</text>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</form>

		<!-- 底部导航栏 -->
		<view id="foot-box" class="cu-bar tabbar bg-white shadow foot">
			<view class="action" @click="MoveSubject(-1)">
				<view class="cuIcon-cu-image">
					<text class="lg cuIcon-back text-gray"></text>
				</view>
				<view class="text-gray">上一题</view>
			</view>
			
			<view class="action" @click="MoveSubject(1)">
				<view class="cuIcon-cu-image">
					<text class="lg text-gray cuIcon-right"></text>
				</view>
				<view class="text-gray">下一题</view>
			</view>

			<view class="action" @click="FavorSubject">
				<view class="cuIcon-cu-image">
					<text class="lg cuIcon-favor" :class="[subjectList[subjectIndex].userFavor?'text-red':'text-gray']"></text>
				</view>
				<view :class="[subjectList[subjectIndex].userFavor?'text-red':'text-gray']">收藏</view>
			</view>

			<!-- 添加语音播放按钮 -->
			<!-- <view class="action" @click="toggleAudio" v-if="subjectList[subjectIndex] && subjectList[subjectIndex].sAudio && subjectList[subjectIndex].sAudio.length>0">
				<view class="cuIcon-cu-image">
					<text class="lg" :class="[isPlaying ? 'cuIcon-stop text-green' : 'cuIcon-voice text-gray']"></text>
				</view>
				<view :class="[isPlaying ? 'text-green' : 'text-gray']">{{ isPlaying ? '暂停' : '语音' }}</view>
			</view> -->

			<view class="action" @tap="showErrorModal" data-target="modalError">
				<view class="cuIcon-cu-image">
					<text class="lg text-gray cuIcon-warn"></text>
				</view>
				<view class="text-gray">纠错</view>
			</view>
			
			<view class="action" data-target="juaojuan" @tap="showModal">
				<view class="cuIcon-cu-image">
					<uni-icons type="checkbox-filled" color="#00e54c" size="40rpx"></uni-icons>
				</view>
				<view class="text-green">交卷</view>
			</view>
		</view>

		<!-- 交卷确认模态框 -->
		<view class="cu-modal" :class="modalName=='juaojuan'?'show':''">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">交卷?</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-xl">
					<view class="score-text">成绩: {{Math.round((userSuccess/subjectList.length)*50)}}分</view>
					<view>总共{{subjectList.length}}题</view>
					<view>答对{{userSuccess}}题</view>
					<view>答错{{userError}}题</view>
					<view v-if="subjectList.length>(userError+userSuccess)">未答{{subjectList.length-(userError+userSuccess)}}题</view>
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="hideModal">继续答题</button>
						<button class="cu-btn bg-green margin-left" data-target="submitScoreView" @tap="submitScore">确定交卷</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 成绩展示模态框 -->
		<view class="cu-modal" :class="submitScoreView=='submitScoreView'?'show':''">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">{{(userSuccess/subjectList.length)*100>=90?'成绩合格':'成绩不合格'}}</view>
				</view>
				<view class="padding-xl">
					<view v-if="time<=0" class="timeout-text">时间到</view>
					<view>考试得分：{{Math.round((userSuccess/subjectList.length)*100)}}</view>
					<view>用时：{{yongshi}}</view>
					<view>
						<view class='cu-tag' :class="(userSuccess/subjectList.length)*100<60?'bg-red':'bg-green'" style="margin-top: 10rpx; width: 200rpx; font-size: 40rpx; font-weight: bold;height: 60rpx;">{{designation}}</view>
					</view>
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn bg-green margin-left" @click="gotoIndex">确定</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return{
				//#ifdef MP-WEIXIN
					// 为了解决微信小程序无法使用全局变量修改所以每个文件都设置了服务端地址
				// api: 'http://www.xzys-mtczy-test.com',
				// api: 'http://*************:8080',
				//#endif
				api: this.$config.baseApi,
				submitScoreView:null,
				startTime:2700,
				heigth:750,
				swHeigth:750,
				time:2700,
				startDate:null,
				djs:'45分00秒',
				userFavor: false, //是否已收藏
				userError: 0, //用户错题数量
				userSuccess: 0, //用户答对题目数量
				currentType: 0, //当前题型
				subjectIndex: 0, //跳转索引
				autoShowAnswer: true, //答错是否显答案
				autoRadioNext: true, //判断题、单项题，自动移下一题
				swiperHeight: '800px', //
				title: '科目一模拟考试',
				yongshi:null,//考试用时，交卷的时候计算
				boxTmp: '', //多选选项缓存
				km:null,
				car:null,
				modalName: null,
				jsq:null,
				errForm:{
					errOption:'0',
					errMsg:''
				},
				subjectList: [{
						'sId': 1,
						"sQuestion": "",
						"sImg":'',
						"sAudio":'',
						"sType": 1,
						"itemA": "A",
						"itemB": "B",
						"itemC": "C",
						"itemD": "D",
						"answer": "B",
						"userAnswer": "",
						"userFavor": false,
						"sExplain": ""
					}


				],
				isPlaying: false, // 添加音频播放状态变量
				audioContext: null, // 添加音频上下文变量
				modalCard: null, //显示答题卡
				modalError: null, //纠错卡
				errorList: ['题目不完整', '答案不正确', '含有错别字', '图片不存在', '解析不完整', '其他错误']
			}
		},
		onLoad(option) {
			let that=this;
			uni.showLoading({title: '加载中',mask:true});
			option.km==0?this.km=1:this.km=4;
			if(option.cartype==4)this.time=this.startTime=1800;//设置摩托车考试时间
			
			this.countdown();//开启倒计时
			this.startDate=new Date();//获得开始时间
			let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
			let curRoute = routes[routes.length - 1].route //获取当前页面路由
			let curParam = routes[routes.length - 1].options; //获取路由参数
			
			let examSet=null;
			this.car=option.cartype;
			//获取当前的考试设置
			
			uni.request({
				method:"POST",
				url:this.api+'/api/findChapterByCarAndKm',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				data:{
					car:option.cartype,
					km:this.km,
				},
				success(resExamSet) {
					// Add null check for resExamSet
					if (!resExamSet || !resExamSet.data) {
						uni.showToast({
							title: '获取考试设置失败',
							icon: 'none'
						});
						setTimeout(function () {uni.hideLoading();}, 100);
						return;
					}
					examSet=resExamSet.data;

					// 随机抽取一个章节id
					let randomChapterId = null;
					if (Array.isArray(examSet) && examSet.length > 0) {
						const randomIndex = Math.floor(Math.random() * examSet.length);
						randomChapterId = examSet[randomIndex].chapterId;
						that.randomChapterName = examSet[randomIndex].chapterName;
						console.log("[fetchQuestions] 随机抽取的章节ID:", randomChapterId);
					}
		
					let params = {
						'sCar': option.cartype,
						'sKm': that.km,
						'state': 'exam', //考试
						'errList': null,
						chapterId: randomChapterId // 新增：传递随机章节id
					};
			
			
					uni.request({
						method: "POST",
						url: that.api + '/api/findSubjectByChapterAndKm',
						data: params,
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						success(res) {
				
							// Add null check for res
							if (!res || !res.data) {
								uni.showToast({
									title: '获取题目失败',
									icon: 'none'
								});
								setTimeout(function () {uni.hideLoading();}, 100);
								return;
							}					

							res=res.data;
							
							let total;
							let newSubjectList=[];
							if(that.km==4){
								total=50;
							}else{
								option.cartype==4?total=50:total=50;
							}

							// Add null check for examSet
							if (!examSet || !examSet.data) {
								uni.showToast({
									title: '考试设置数据异常',
									icon: 'none'
								});
								setTimeout(function () {uni.hideLoading();}, 100);
								return;
							}					

							examSet=examSet.data;
					
							let xuanzheAll=res.filter(item => item.sType==1);//.sort(() => Math.random() - 0.5);
							let panduanAll=res.filter(item => item.sType==2);//sort(() => Math.random() - 0.5);
							let duoxuanAll=res.filter(item => item.sType==3);//sort(() => Math.random() - 0.5);
							console.log(examSet);

							/*for(let i=0;i<examSet.length;i++){
								//let zhangjieAll=res.filter(item => item.chapterId==examSet[i].chapter_id);//筛选改章节的题目
								// 筛选后打乱顺序放到对应的数组
								let xuanzhe =xuanzheAll.filter(item => item.chapterId==examSet[i].chapter_id);//.sort(() => Math.random() - 0.5);//选择题
								let panduan =panduanAll.filter(item => item.chapterId==examSet[i].chapter_id);//.sort(() => Math.random() - 0.5);//判断题
								let duoxuan =duoxuanAll.filter(item => item.chapterId==examSet[i].chapter_id);//.sort(() => Math.random() - 0.5);//多选题
								if(that.km==1){//设置科目一考试题目
									if(option.cartype==4){//设置摩托车科目一考试题目
										panduan=panduan.slice(0,0.2*examSet[i].proportion);
										xuanzhe=xuanzhe.slice(0,0.3*examSet[i].proportion);
										newSubjectList = newSubjectList.concat(panduan);
										newSubjectList = newSubjectList.concat(xuanzhe);
									}
									
								}else{//科目四设置
								
									panduan=panduan.slice(0,0.2*examSet[i].proportion);
									xuanzhe=xuanzhe.slice(0,0.2*examSet[i].proportion);
									
									duoxuan=duoxuan.slice(0,0.1*examSet[i].proportion);
								
									newSubjectList = newSubjectList.concat(panduan);
									newSubjectList = newSubjectList.concat(xuanzhe);
									newSubjectList = newSubjectList.concat(duoxuan);
								}
							}*/
							console.log('----------------newSubjectList---------------'+newSubjectList.length);
							let sortArr=res;
							//let sortArr=[];//用于排序;
							// if(that.km==1){//科目一排序
					
							// 	let arr=newSubjectList.filter(item => item.sType==2);
							// 	let arr1=newSubjectList.filter(item => item.sType==1);
							// 	arr=that.getResList(panduanAll,arr,total*0.2);
							// 	arr1=that.getResList(xuanzheAll,arr1,total*0.3);
							// 	sortArr = sortArr.concat(arr);
							// 	sortArr = sortArr.concat(arr1);
							// }else if(that.km==4){//科目四排序
							// 	let arr=newSubjectList.filter(item => item.sType==2);
							// 	let arr1=newSubjectList.filter(item => item.sType==1);
							// 	let arr2=newSubjectList.filter(item => item.sType==3);
							
							// 	arr=that.getResList(panduanAll,arr,total*0.4);
							// 	arr1=that.getResList(xuanzheAll,arr1,total*0.4);
							// 	arr2=that.getResList(duoxuanAll,arr2,total*0.2);
							

							// 	sortArr = sortArr.concat(arr);
							// 	sortArr = sortArr.concat(arr1);
							// 	sortArr = sortArr.concat(arr2);
							// }
							// console.log(sortArr);

							sortArr.sort((a, b) => a.sId - b.sId); // 按 sId 从小到大排序
							that.subjectList = sortArr;//newSubjectList;//sortArr;
							
							if (that.subjectList.length === 0) {
								uni.showToast({
									title: '未能生成有效的题目列表，请检查是否有设置章节比例。',
									icon: 'none'
								});
							} else if (that.subjectList.length < totalQuestionsTarget) {
								uni.showToast({
									title: `共生成了${that.subjectList.length}题（少于目标${totalQuestionsTarget}题）`,
									icon: 'none'
								});
							} else {
								uni.showToast({
									title: `本次随机抽取章节${this.randomChapterName}，共生成了${that.subjectList.length}题`,
									icon: 'success'
								});
							}

							let km1SubjectList=uni.getStorageSync("km1SubjectList");
							for(let item of km1SubjectList){
								item.userFavor=false;
							}
									
							/**
							 * 遍历用户收藏
							 */
							if(uni.getStorageSync("userInfo").uCollection!=undefined){
								let uCollection = uni.getStorageSync("userInfo").uCollection;
								let carr = uCollection.split(',');
								for(let i of carr){
									for(let s of that.subjectList){
										if(s.sId == i){
											s.userFavor=true;
											break;
										}
									}
								}
																
							}
					
							that.setHeigth();
							setTimeout(function () {uni.hideLoading();}, 100);

							that.currentType = that.subjectList[0].sType;
							uni.setNavigationBarTitle({
								title: that.km==1?'科目一模拟考试':'科目四模拟考试'
							});
							
							//添加用户显示答案字段
							for (var i = 0; i < that.subjectList.length; i++) {
								that.$set(that.subjectList[i], "showAnswer", false);
							}
						},
						fail(err) {
							console.log(err);
							uni.showToast({
								title: '获取题目失败',
								icon: 'none'
							});
							setTimeout(function () {uni.hideLoading();}, 100);
						}
					});
				},
				fail(err) {
					console.log(err);
					uni.showToast({
						title: '获取考试设置失败',
						icon: 'none'
					});
					setTimeout(function () {uni.hideLoading();}, 100);
				}
    		});
		},
		onReady() {
		
			var tempHeight = 800;
			var _me = this;
			uni.getSystemInfo({
				//获取手机屏幕高度信息，让swiper的高度和手机屏幕一样高                
				success: function(res) {
				                  
					tempHeight = res.windowHeight;
					
		
					console.log("屏幕可用高度 " + tempHeight);
					console.log("屏幕可用宽度 " + res.windowWidth);
					uni.createSelectorQuery().select("#top-box").fields({
						size: true,
						scrollOffset: true
					}, (data) => {				
						console.log("减掉顶部后的高度 " + tempHeight);
		
						uni.createSelectorQuery().select("#foot-box").fields({
							size: true,
							scrollOffset: true
						}, (data) => {
							tempHeight -= data.height;
							console.log("减掉底部后的高度 " + tempHeight);
							_me.swiperHeight = tempHeight + 'px';
							_me.swHeigth=tempHeight;
							console.log("滑屏最后高度 " + _me.swiperHeight);
						}).exec();
		
					}).exec();
				}
			});
		
		},
		
		onHide(){
			clearInterval(this.jsq);
			// 停止音频播放
			if (this.audioContext) {
                this.audioContext.stop();
                this.isPlaying = false;
            }
		},
		onUnload(){
			clearInterval(this.jsq);
			// 销毁音频上下文
			if (this.audioContext) {
                this.audioContext.destroy();
                this.audioContext = null;
                this.isPlaying = false;
            }
		},
		onShow() {
			clearInterval(this.jsq);
			this.countdown();
		},
		
		methods:{
			/**
			 * @param {Object} oo 存放一个题目类型的数组如 (存放所有选择题的数组)
			 * @param {Object} nn 结果数组,经过层层筛选后得到的数组
			 * @param {Object} y  数组需要多少条题目
			 */
			getResList(oo,nn,y){
				let g=y-nn.length
				
				for(let o of oo ){
					if(g==0)break;
										
					let bool =true;
					for(let n of nn){
						if(o.sId==n.sId)bool=false;
					}
					
					if(bool){
						
						nn.push(o);
						g--;
					}
					
					
					
				}
				
				return nn;
			},
			errRadioChange(e){
					
				  for (let i = 0; i < this.errorList.length; i++) {
						if (i == e.detail.value) {
							this.errForm.errOption = i+'';
							break;
						}
					}
			},
			setHeigth(){
				let that=this;
				
				uni.createSelectorQuery().select("#swid"+ that.subjectIndex).fields({
					size: true,
					scrollOffset: true
				}, (data) => {
					console.log(data);
					if(data==null){
						
						return;
					}
					that.heigth=that.swHeigth>data.height?that.swHeigth:data.height+60;
					
				}).exec();
				
			},
			
			updateUserCollection(){//更新收藏
				uni.request({
					method: "POST",
					url: this.api + '/api/updateUserCollection',
					data: {
						uId:uni.getStorageSync("userInfo").uId,
						uCollection:uni.getStorageSync("userInfo").uCollection
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: (res) => {
						console.log(res);
					}
				})
			},
			
			// 确认交卷按钮
			submitScore(e){
				let that=this;
				clearInterval(this.jsq)
				this.modalName=null;
				this.submitScoreView = 'submitScoreView';
				let nowdata=new Date();
				let fen = Math.floor((nowdata-this.startDate)/1000/60);
				let miao =Math.floor(((nowdata-this.startDate)%60000)/1000);
				
				this.yongshi=fen+'分'+miao+'秒';
				let userInfo=uni.getStorageSync("userInfo");
				if(userInfo=='')return;//判断登录没登录则不提交成绩
				let score=Math.round((this.userSuccess/this.subjectList.length)*100);
				 let date1=new Date('2020-1-1 00:00:00')
				let yongshi=new Date((fen*60*1000)+(miao*1000)+date1.getTime());
				uni.request({
					method:"POST",
					
					data:{
						uId:userInfo.uId,
						km:this.km,
						carType:this.car,
						score:score,
						useTime:yongshi,
						examTime:this.startDate,
						successCount:this.userSuccess,
						errorCount:this.userError,
						unan:this.subjectList.length-this.userSuccess-this.userError
					},
					url:this.api+'/api/userSubmitScore',
					
					success(res) {
						console.log(res);
					}
				})
			
			},
			countdown(){
				let that=this;
				
					that.jsq=setInterval(function(){
						that.time=that.time-1;
					   var minute=parseInt(that.time/60);
					   var second=parseInt(that.time%60);
						console.log('还剩'+minute+'分'+second+'秒');
						that.djs=minute+'分'+second+'秒';
						that.$forceUpdate();
						
						if (that.time<=0){
							clearInterval(that.jsq)
							that.submitScore();//交卷
						}
						
					},1000);
					
				
			},
			
				showCardModal: function(e) {
					this.modalCard = e.currentTarget.dataset.target
				},
				hideCardModal: function(e) {
					this.modalCard = null
				},
				showErrorModal: function(e) {
					this.modalError = e.currentTarget.dataset.target
				},
				hideErrorModal: function(e) {
					this.modalError = null
				},
				SwiperChange: function(e) { //滑动事件
			        // 如果正在播放音频，停止播放
					if (this.isPlaying && this.audioContext) {
						this.audioContext.stop();
						this.isPlaying = false;
					}

					let index = e.target.current;
			
					if (index != undefined) {
						this.subjectIndex = index;
						this.currentType = this.subjectList[index].sType;
						this.userFavor = this.subjectList[index].userFavor;
					}
					this.setHeigth();
				},
				RadioboxChange: function(e) { //单选选中
			
					let that = this;
			
					var values = e.detail.value;
			
					if (e.detail.value == this.subjectList[this.subjectIndex].answer) {
						this.userSuccess++;
						this.subjectList[this.subjectIndex].userAnswer = values;
						this.ShowAnswerChange();
						if (this.autoRadioNext && this.subjectIndex < this.subjectList.length - 1) {
							this.subjectIndex += 1;
						};
			
					} else {
						
						let userErrorSubjectList = uni.getStorageSync('userErrorSubjectList').length <= 0 ? [] : uni
							.getStorageSync('userErrorSubjectList');
				
						
						// 用户答错题判断缓存用户错题里存不存在这道题,不存在就添加
						if (userErrorSubjectList.indexOf(this.subjectList[this.subjectIndex].sId) == -1) {
							userErrorSubjectList.push(this.subjectList[this.subjectIndex].sId);
							uni.setStorageSync('userErrorSubjectList', userErrorSubjectList);
						}
						
						this.userError++;
						this.subjectList[this.subjectIndex].userAnswer = values;
						this.ShowAnswerChange();
						
					}
				
			
			
				},
				CheckboxChange: function(e) { //复选选中
			
					var values = e.detail.value;
					this.boxTmp = e.detail.value.toString();
			
				},
				fxqd: function() { //复选确定按钮
				
					console.log(this.getWidth())
					if (this.boxTmp.length > 0) {
			
			
						this.subjectList[this.subjectIndex].userAnswer = this.boxTmp.toString().replace(/,/g,'');
						this.boxTmp = '';
						console.log("用户答案:" + this.subjectList[this.subjectIndex].userAnswer)
						//用户选对了答案
						if (this.subjectList[this.subjectIndex].userAnswer == this.subjectList[this.subjectIndex].answer) {
							this.userSuccess++;
							this.ShowAnswerChange();
							if (this.autoRadioNext && this.subjectIndex < this.subjectList.length - 1) {
								this.subjectIndex += 1;
							};
						} else {
							let userErrorSubjectList = uni.getStorageSync('userErrorSubjectList').length <= 0 ? [] : uni
								.getStorageSync('userErrorSubjectList');
											
							
							// 用户答错题判断缓存用户错题里存不存在这道题,不存在就添加
							if (userErrorSubjectList.indexOf(this.subjectList[this.subjectIndex].sId) == -1) {
								userErrorSubjectList.push(this.subjectList[this.subjectIndex].sId);
								uni.setStorageSync('userErrorSubjectList', userErrorSubjectList);
							}
							
							
							this.userError++;
							this.ShowAnswerChange();
						}
					} else {
						uni.showToast({
							title: this.windowWidth,
							icon: 'none',
							duration: 1000
						})
						uni.showToast({
							title: "您还没有任何选择",
							icon: 'none',
							duration: 1000
						})
					}
			
				},
			
				ShowAnswerChange: function(e) { //显示答案
			
					// if (this.subjectList[this.subjectIndex].showAnswer) {
						//this.subjectList[this.subjectIndex].showAnswer = false;
					// } else {
			
						this.subjectList[this.subjectIndex].showAnswer = true;
						this.heigth+=320;
					//}
				},
			
				FavorSubject: function(e) { //收藏题
			
					/**
					 * 判断登录
					 */
					if(uni.getStorageSync("userInfo")==''){
					
						uni.showToast({
							title:"需要登录",
							icon:"error"
						})
						return;
					}
					
					if (this.subjectList[this.subjectIndex].userFavor) {
						
						
						this.subjectList[this.subjectIndex].userFavor = false;
						let userInfo = uni.getStorageSync("userInfo");
						let carr = userInfo.uCollection.split(',');
						this.removeByVal(carr,this.subjectList[this.subjectIndex].sId);//删除Sid
						userInfo.uCollection=carr.toString();
						uni.setStorageSync("userInfo",userInfo);
						uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存
						this.updateUserCollection();
					} else {
						let userInfo = uni.getStorageSync("userInfo");
						userInfo.uCollection+=','+this.subjectList[this.subjectIndex].sId;
						uni.setStorageSync("userInfo",userInfo);
						
						this.subjectList[this.subjectIndex].userFavor = true;
						uni.setStorageSync("km1SubjectList",this.subjectList);//记录到缓存
						this.updateUserCollection();
					}
				},
			
				MoveSubject: function(e) { //上一题、下一题
			
					if (e === -1 && this.subjectIndex != 0) {
						this.subjectIndex -= 1;
					}
					if (e === 1 && this.subjectIndex < this.subjectList.length - 1) {
						this.subjectIndex += 1;
					}
				},
			
				AppointedSubject: function(e) { //题卡指定
			
					this.modalCard = null;
					this.subjectIndex = e;
				},
			
				SubmitError: function(e) {  //提交纠错
				if(uni.getStorageSync("userInfo")==''){
				
					uni.showToast({
						title:"需要登录",
						icon:"error"
					})
					return;
				}
				
				if(this.errForm.errMsg.length<5){
					uni.showModal({
						content:"至少输入5个字符的描述",
						showCancel:false
					})
					return;
				}else{
					let sId=Number.parseInt(this.subjectList[this.subjectIndex].sId);
					let uId=Number.parseInt( uni.getStorageSync("userInfo").uId);
					let str=this.errorList[Number.parseInt(this.errForm.errOption)]+',';
					uni.request({
						url:this.api+'/api/postErrorSubjectFB',
						method:"POST",
						data:{
							sId:sId,
							uId:uId,
							errMsg:str+this.errForm.errMsg
						},
						header:{
							'content-type': 'application/x-www-form-urlencoded',
						},
						success: (res) => {
							
							uni.showToast({
								icon:"success",
								title:res.data=='success'?'提交成功':'错误'
							})
							this.errForm.errMsg='';
						}
					})
					
					
				this.modalError = null;
				
				}
				},
				getWidth: function() { //返回屏幕宽度-60
					var w;
					uni.getSystemInfo({
						success(res) {
							w = res.windowWidth;
						}
					})
					return w - 60;
				},
				showModal(e) {//交卷对话框
				
					this.modalName = e.currentTarget.dataset.target
				},
				hideModal(e) {//交卷对话框隐蔽
					this.modalName = null
				},
				gotoIndex(){
					uni.switchTab({
						url:'../home/<USER>'
					})
				},

    // 切换音频播放状态
    toggleAudio() {
        if (!this.subjectList[this.subjectIndex].sAudio) return;
        
        if (!this.audioContext) {
            this.audioContext = uni.createInnerAudioContext();
            this.audioContext.onEnded(() => {
                this.isPlaying = false;
            });
            this.audioContext.onError((res) => {
                console.error('音频播放错误:', res);
                uni.showToast({
                    title: '音频播放失败',
                    icon: 'none'
                });
                this.isPlaying = false;
            });
        }
        
        if (this.isPlaying) {
            // 暂停播放
            this.audioContext.pause();
            this.isPlaying = false;
        } else {
            // 开始播放
            const audioUrl = this.api + this.subjectList[this.subjectIndex].sAudio;
            this.audioContext.src = audioUrl;
            this.audioContext.play();
            this.isPlaying = true;
        }
    },				

	onHide(){
		clearInterval(this.jsq);
		// 停止音频播放
		if (this.audioContext) {
			this.audioContext.stop();
			this.isPlaying = false;
		}
},
onUnload(){
    clearInterval(this.jsq);
    // 销毁音频上下文
    if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
        this.isPlaying = false;
    }
},	

		},
		onBackPress(e) {//监听用户返回前一页
            // 停止音频播放
            if (this.audioContext) {
                this.audioContext.stop();
                this.isPlaying = false;
                this.audioContext.destroy();
                this.audioContext = null;
            }

			if(this.showMask) {  
			     this.showMask = false;  
			     return true;  
			   }else{  
			      uni.showModal({  
			        title: '提示',  
			        content: '正在考试,是否退出?',  
			        success: function(res) {  
			            if (res.confirm) {  
							clearInterval(this.jsq);
			                // 退出当前应用，改方法只在App中生效  
			               uni.switchTab({
			                   url: '/pages/home/<USER>'
			               });
						   
			            } else if (res.cancel) {  
			                console.log('用户点击取消');  
			            }  
			        }  
			      });  
			      return true  
			   }  
		},
		computed:{
			designation(){
				let score=(this.userSuccess/this.subjectList.length)*100;
				let str='';
				switch (Math.floor(score/10)){
					case 10: str='驾考传奇'; break;
					case 9: str='驾考车神';break;
					case 8: str='预备车手';break;
					case 7: str='进击的菜鸟';break;
					case 6: str='进击的菜鸟';break;
					default:str='马路杀手';break;
				}
				return str;
			},
			//计算考试用时
			jisuanYongshi(){
				
			}
		}
		
	}

</script>

<style>
	@import "../../colorui/animation.css";
	
	page {
		background-color: #FFFFFF;
	}
	
	/* 考试容器 */
	.exam-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}
	
	/* 固定顶部 */
	.fixed-top {
		position: fixed;
		z-index: 100;
		width: 100%;
		background-color: #fff;
	}
	
	/* 倒计时进度条 */
	.countdown-bar {
		padding: 10rpx 20rpx;
		background-color: #f8f8f8;
	}
	
	.countdown-time {
		text-align: center;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 5rpx;
	}
	
	/* 题目类型标签 */
	.question-type {
		font-size: 28rpx;
		padding: 4rpx 12rpx;
		background-color: #f0f9eb;
		color: #67c23a;
		border-radius: 6rpx;
		margin-right: 10rpx;
	}
	
	/* 主要内容区域 */
	.swiper-box {
		margin-top: 170rpx;
	}
	
	.question-container {
		padding: 20rpx;
	}
	
	/* 题目标题 */
	.question-title {
		display: flex;
		align-items: flex-start;
		padding: 20rpx 0;
		border-bottom: 1px solid #eee;
	}
	
	.question-text {
		font-size: 32rpx;
		line-height: 1.5;
		color: #333;
		flex: 1;
	}
	
	/* 图片容器 */
	.image-container {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
	}
	
	.question-image {
		max-width: 90%;
		width: 600rpx;
		border-radius: 8rpx;
	}
	
	/* 选项容器 */
	.options-container {
		margin-top: 20rpx;
	}
	
	.option-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		margin-bottom: 15rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
	}
	
	.option-icon {
		width: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.option-content {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		padding-left: 10rpx;
	}
	
	/* 答案解析 */
	.answer-explanation {
		margin-top: 30rpx;
		padding: 20rpx;
		background-color: #f0f9eb;
		border-radius: 8rpx;
		border-left: 4rpx solid #67c23a;
	}
	
	.explanation-item {
		display: line;
		margin-bottom: 10rpx;
		flex-wrap: nowrap; /* 防止换行 */
		align-items: center; /* 垂直居中对齐 */
	}
	
	.explanation-label {
		color: #666;
		width: 150rpx;
		min-width: 150rpx; /* 给标签一个固定的最小宽度 */
	}
	
	.explanation-title {
		color: #666;
		/* margin: 15rpx 0 10rpx; */
		margin-top: 10rpx;
		margin-bottom: 5rpx;		
		font-weight: bold;
	}
	
	.explanation-content {
		flex: 1;
		/* line-height: 1.5; */
		word-break: break-all; /* 允许在任意字符间断行 */
	}
	
	/* 纠错模态框 */
	.error-textarea {
		width: 100%;
		height: 200rpx;
		border: 1px solid #ccc;
		padding: 10rpx;
		text-align: left;
		border-radius: 6rpx;
	}
	
	/* 交卷模态框 */
	.score-text {
		font-size: 40rpx;
		font-weight: bold;
		color: #67c23a;
		margin-bottom: 20rpx;
	}
	
	.timeout-text {
		font-size: 36rpx;
		color: #f56c6c;
		margin-bottom: 20rpx;
	}
	
	/* 底部导航栏 */
	.foot {
		position: fixed;
		bottom: 0;
		width: 100%;
	}
	
	/* 适配藏文 */
	.tibetan-text {
		font-size: 32rpx;
		line-height: 1.8;
	}
	
	/* 辅助样式 */
	.padding-left-sm {
		padding-left: 20rpx;
	}
	
	.cu-form-group {
		justify-content: flex-start;
	}
	
	.cu-form-group .title {
		padding-left: 30upx;
		padding-right: 0upx;
	}
	
	.cu-form-group+.cu-form-group {
		border-top: none;
	}
	
	.cu-bar-title {
		min-height: 50upx;
	}
	
	.cu-list.menu>.cu-item-error {
		justify-content: flex-start;
	}
	
	.min-height30 {
		min-height: 30px;
	}
	
	.margin-r-l {
		margin: 0 10px;
	}
</style>