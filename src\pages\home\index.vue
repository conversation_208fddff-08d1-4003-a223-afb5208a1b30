<template>
	<view  class="page-container">


		<!-- 轮播图 -->
		<swiper lazy-load class="screen-swiper square-dot" :indicator-dots="true" :circular="true" :autoplay="true"
			interval="2000" duration="1000">
			<swiper-item v-for="(item, index) in bannerList" :key="index">
				<image :src="api + item.imgUrl" mode="aspectFill"
					type='image'></image>
			</swiper-item>
			
		</swiper>
		<!-- k科目导航 -->
		<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button"
			activeColor="#00aaff"></uni-segmented-control>

		<!-- 功能入口网格 -->
		<!-- {{ edit_1: 更新网格内容和结构以匹配图片样式 }} -->
		<view class="grid-container padding">
			<!-- 顺序练习 -->
			<view @click="navigateTo('/pages/dati/index?state=sunxu&cartype='+userCheckCat+'&km='+current)"
				hover-class="navigator-hover" class="grid-item" style="background-color: #1abc9c;"> <!-- Teal -->
				<view class="item-content">
					<view class="icon-background">
						<uni-icons type="list" size="30" color="#ffffff"></uni-icons>
					</view>
					<text class="grid-text">顺序练习</text>
					<text class="grid-subtext" v-if="subjectTotal > 0">{{subjectTotal}}题</text> <!-- 保留题目数量显示 -->
				</view>
			</view>

			<!-- 模拟考试 -->
			<view @click="navigateTo('/pages/examIndex/examIndex?cartype='+userCheckCat+'&km='+current)"
				hover-class="navigator-hover" class="grid-item" style="background-color: #f39c12;"> <!-- Orange -->
				<view class="item-content">
					<view class="icon-background">
						<!-- 选择一个合适的图标，例如 'paperplane-filled' 或自定义图标 -->
						<uni-icons type="paperplane-filled" size="30" color="#ffffff"></uni-icons>
					</view>
					<text class="grid-text">模拟考试</text>
				</view>
			</view>

			<!-- 随机练习 -->
			<view @click="navigateTo('/pages/dati/index?state=suiji&cartype='+userCheckCat+'&km='+current)"
				hover-class="navigator-hover" class="grid-item" style="background-color: #8bc34a;"> <!-- Light Green -->
				<view class="item-content">
					<view class="icon-background">
						<uni-icons type="refreshempty" size="30" color="#ffffff"></uni-icons>
					</view>
					<text class="grid-text">随机练习</text>
				</view>
			</view>

			<!-- 章节练习 (Placeholder URL) -->
			<view @click="navigateTo('#')" hover-class="navigator-hover" class="grid-item" style="background-color: #e91e63;"> <!-- Pink/Red -->
				<view class="item-content">
					<view class="icon-background">
						<uni-icons type="bars" size="30" color="#ffffff"></uni-icons> <!-- Book icon -->
					</view>
					<text class="grid-text">章节练习</text>
				</view>
			</view>

			<!-- 错题练习 -->
			<view @click="navigateTo('/pages/WrongQuestionIndex/WrongQuestionIndex?cartype='+userCheckCat+'&km='+(current==0?1:4))"
				hover-class="navigator-hover" class="grid-item" style="background-color: #2196f3;"> <!-- Blue -->
				<view class="item-content">
					<view class="icon-background">
						<uni-icons type="close" size="30" color="#ffffff"></uni-icons> <!-- Simple X icon -->
					</view>
					<text class="grid-text">错题练习</text>
				</view>
			</view>

			<!-- 收藏练习 (Placeholder URL) -->
			<view @click="navigateTo('#')" hover-class="navigator-hover" class="grid-item" style="background-color: #4caf50;"> <!-- Dark Green -->
				<view class="item-content">
					<view class="icon-background">
						<uni-icons type="star-filled" size="30" color="#ffffff"></uni-icons> <!-- Star icon -->
					</view>
					<text class="grid-text">收藏练习</text>
				</view>
			</view>
		</view>
		<!-- {{ /edit_1 }} -->
	</view>
</template>
<script>
	export default {

		data() {
			return {
				api: this.$config.baseApi,
				items: ["科目一", "科目四"],
				current: 0,
				userCheckCat: 4,
				subjectTotal:0,
				bannerList: [], // 存储轮播图数据
			}
		},
		methods: {

			// 获取轮播图数据
			getBannerList() {
				let that = this;
				// console.log('正在获取轮播图数据...');
				// 先设置多个默认图片，确保轮播图立即显示且有轮播效果
				that.bannerList = [
					{ imgUrl: '/uploadFile/banner/banner3.jpg' },
					{ imgUrl: '/uploadFile/banner/banner3.jpg' },
					{ imgUrl: '/uploadFile/banner/banner3.jpg' }
				];

				// 使用setTimeout延迟加载实际数据，让界面先渲染出来
				// setTimeout(() => {				
					uni.request({
						url: that.api + '/api/getBannerList',
						method: 'GET',
						success(res) {
							if (res.data && res.data.length > 0) {
								// console.log('获取轮播图数据'+res.data);
								that.bannerList = res.data;
							} else {
								// 如果没有数据，使用默认图片
								that.bannerList = [{
									imgUrl: '/uploadFile/banner/banner3.jpg'
								}];
							}
						},
						fail(err) {
							console.error('获取轮播图失败:', err);
							// 加载失败时使用默认图片
							that.bannerList = [{
								imgUrl: '/uploadFile/banner/banner3.jpg'
							}];
						},
						complete() {
							// 确保请求完成后页面刷新
							that.$forceUpdate();
						}
					});
				// }, 100); // 延迟100毫秒加载数据
			},
			

			gotoScore(){
				if(this.checkUserLogin()==false)return;
				uni.navigateTo({
					url:'/pages/score/score?cartype='+this.userCheckCat+'&km='+this.current
				});
				
			},
			    // 统一处理导航，先检查登录状态
			navigateTo(url) {
				// 先检查登录状态
				if (!this.checkUserLogin()) {
					return;
				}
				
				// 已登录且URL有效，则进行跳转
				if (url !== '#') {

					// 显示加载提示，防止用户重复点击
					uni.showLoading({
						title: '加载中...',
						mask: true
					});					
					// 预先设置默认数据，防止返回时白屏
					this.bannerList = [
						{ imgUrl: '/uploadFile/banner/banner3.jpg' },
						{ imgUrl: '/uploadFile/banner/banner3.jpg' }
					];
					
					// 使用setTimeout确保加载提示显示出来
					setTimeout(() => {
						uni.navigateTo({
							url: url,
							success: () => {
								// 导航成功后隐藏加载提示
								// 使用try-catch防止hideLoading失败
								try {
									uni.hideLoading();
								} catch (e) {
									console.log('hideLoading error:', e);
								}
							},
							fail: (err) => {
								console.error('导航失败:', err);
								// 导航失败时提示用户
								try {
									uni.hideLoading();
								} catch (e) {
									console.log('hideLoading error:', e);
								}
								uni.showToast({
									title: '页面加载失败，请重试',
									icon: 'none'
								});

							}
						});
					}, 100);
				}
			},
			checkUserLogin(){
				console.log(uni.getStorageSync("userInfo"));
					  if(uni.getStorageSync("userInfo")==undefined || uni.getStorageSync("userInfo")==''){
					  		  uni.showToast({
					  		  	icon:"error",
					  			title:"您还没有登录!"
					  		  })
					  		  return false;
					  }
					  return true;
			},
			onClickItem(e) {
			
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex;
					
					uni.setStorageSync("userCheckKm",e.currentIndex==0?1:4)//设置用户科目缓存
					let log = this.current == 0 ? '您已切换到科目一' : '您已切换到科目四';
					uni.showToast({
						title: log,
						icon: 'none',
						duration: 1000
					});
					console.log(this.current,'curr');
					this.getTotal();
				}
			},
			log() {
				console.log(123)
			},
			getTotal(){
				let that=this;
				uni.showLoading({title: '加载中',mask:true});
				uni.request({
					method:"POST",
					url:that.api+'/api/getSubjectTotalByCarAndKm',
					header:{'Content-Type': 'application/x-www-form-urlencoded'},
					data:{
						"sCar":that.userCheckCat,
						"sKm":that.current==0?1:4
					},
					success(res) {
						console.log(res.data);
						that.subjectTotal=res.data;
						setTimeout(function () {uni.hideLoading();}, 100);
					},
					fail(err) {
						console.log(err);
						// 添加错误提示
						uni.showToast({ title: '获取题目总数失败', icon: 'none' });
					}
					
					
				})
			},
			checkUserState(){
					  let that=this;
					  let userInfo=uni.getStorageSync("userInfo");
					  if(userInfo!==''){
						  uni.request({
						  	url:this.api+"/api/userLogin",
						  	method:"POST",
							data:{uNumber:userInfo.uNumber,uPassword:userInfo.uPassword},
							success: (res) => {
								if(res.data.uState!='0'){
									uni.showModal({
										content: '账号异常!',
										showCancel: false,
									});
									uni.removeStorageSync("userInfo");
									that.userInfo=null;
									uni.redirectTo({
										url:"/pages/mine/index"
									})
								}else{
									uni.setStorageSync("userInfo",res.data);
								
								//console.log(res.data);
								}
							}
						  })
					  }
			},
			checkUserLogin(){
				// 获取用户信息
				let userInfo = uni.getStorageSync("userInfo");
				console.log(userInfo);
				
				// 检查是否登录
				if(userInfo == undefined || userInfo == ''){
					uni.showModal({

						content: '您还没有登录，请先登录后使用此功能',
						showCancel: false,
						success: function (res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/login/login'
								});
							}
						}
					});
					return false;
				}
				
				// 检查登录是否过期
				let lastActiveTime = uni.getStorageSync("lastActiveTime");
				let currentTime = new Date().getTime();
				
				// 如果超过30分钟没有操作，视为登录过期
				if (lastActiveTime && (currentTime - lastActiveTime > 30 * 60 * 1000)) {
					uni.showModal({
						title: '登录已过期',
						content: '您的登录已过期，请重新登录',
						showCancel: false,
						success: function () {
							uni.removeStorageSync("userInfo");
							uni.removeStorageSync("lastActiveTime");
							uni.navigateTo({
								url: '/pages/auth/login/login'
							});
						}
					});
					return false;
				}
				
				// 更新最后活动时间
				uni.setStorageSync("lastActiveTime", currentTime);
				return true;
			},

		},
		components: {

		},
		onLoad() {
			let that = this;

			// 先设置默认轮播图，确保页面立即渲染
			that.bannerList = [
				{ imgUrl: '/uploadFile/banner/banner3.jpg' },
				{ imgUrl: '/uploadFile/banner/banner3.jpg' }
			];
    		console.log(uni.getStorageSync("userCheckKm"), '------------');

			that.current = uni.getStorageSync("userCheckKm") == 4 ? 1 : 0;
			that.getTotal();
			that.checkUserState();
			// 获取轮播图数据
			that.getBannerList();

			// 使用setTimeout延迟加载其他数据，让轮播图先渲染
			// setTimeout(() => {
			// 	that.getTotal();
			// 	that.checkUserState();
			// 	that.checkUserLogin();
			// }, 100);

			// uni.setStorageSync("userCheckKm",1);
			// 检查登录状态
			that.checkUserLogin();
			// 初始化最后活动时间
			if (!uni.getStorageSync("lastActiveTime")) {
        		uni.setStorageSync("lastActiveTime", new Date().getTime());
    		}
			
		},
		
		onShow() {
			// 每次页面显示时更新活动时间
			uni.setStorageSync("lastActiveTime", new Date().getTime());
			// 刷新轮播图数据
			// this.getBannerList();			

		},
		// 添加页面隐藏时的处理
		onHide() {
			// 页面隐藏时预先设置默认数据，防止切换回来时白屏
			this.bannerList = [
				{ imgUrl: '/uploadFile/banner/banner3.jpg' },
				{ imgUrl: '/uploadFile/banner/banner3.jpg' }
			];
		},
		
	}
</script>
<style>
	@import "../../colorui/animation.css";

	/* 添加轮播图样式 */
	.screen-swiper {
		height: 350rpx;
		width: 100%;
		margin-bottom: 20rpx;
		background-color: #f0f0f0; /* 添加背景色，防止白屏 */
		position: relative;
	}
	
	.screen-swiper image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	/* {{ edit_10: 添加新的样式规则，并移除不再需要的旧样式 }} */
	.page-container {
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.grid-container {
		display: flex;
		flex-wrap: wrap;
		/* {{ edit_4: 使用 space-between 让项目靠两边，中间留空隙 }} */
		justify-content: space-between;
		background-color: transparent; /* 容器背景透明 */
		border-radius: 0; /* 移除容器圆角 */
		margin: 20rpx;
		padding: 0; /* 移除容器内边距 */
		box-shadow: none; /* 移除容器阴影 */
	}

	.grid-item {
		/* {{ edit_5: 调整宽度和边距以匹配图片 }} */
		width: 48%; /* 每行显示 2 个，留出中间空隙 */
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx; /* 项目之间的垂直间距 */
		padding: 40rpx 10rpx; /* 调整垂直和水平内边距 */
		box-sizing: border-box;
		/* background-color: #fff; */ /* 背景色由内联样式指定 */
		border-radius: 16rpx; /* 设置圆角 */
		/* 添加阴影效果 */
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.item-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
	}

	/* {{ edit_6: 添加图标圆形背景样式 }} */
	.icon-background {
		width: 100rpx; /* 圆形背景宽度 */
		height: 100rpx; /* 圆形背景高度 */
		border-radius: 50%; /* 设置为圆形 */
		background-color: rgba(255, 255, 255, 0.2); /* 半透明白色背景 */
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx; /* 图标和文字的间距 */
	}
	/* {{ /edit_6 }} */

	.grid-text {
		/* {{ edit_7: 设置文字颜色为白色 }} */
		font-size: 30rpx; /* 稍大字体 */
		color: #ffffff;
		margin-top: 0; /* 移除与上方图标背景的额外间距 */
	}

	.grid-subtext {
		/* {{ edit_8: 副标题也设为白色或浅色 }} */
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8); /* 稍透明的白色 */
		margin-top: 4rpx;
	}

	/* 保留您原有的其他样式 */
	.button-yuanjiao {
		width: 200rpx;
		height: 200rpx;
		display: flex;
		/* margin-top: 30rpx; */
		padding-top: 30px;
		line-height: 30rpx;
		justify-content: center;
		border-radius: 100px;
		color: #ffffff;
		background-color: #0ccd97;
		font-size: 30rpx;
		border-color: red;
		border-width: 4px;


	}

	.bg-click {
		background-color: #a7a9ff;
	}

	.bg-click_A {
		content: " ";
		width: 200rpx;
		height: 200rpx;
		display: flex;
		/* 		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid rgba(0,0,0,.2);
		-webkit-transform: scale(.5);
		transform: scale(.5);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		box-sizing: border-box;
		border-radius: 10px; */
	}
</style>
