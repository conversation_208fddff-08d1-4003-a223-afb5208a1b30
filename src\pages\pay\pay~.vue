<template>
    <view class="booking_div_bar">
      <view class="price-data">
        <view>
          <view style="display:flex;height:42rpx;align-items: flex-end;">
            <view class="price-data-title">报名费用</view>
            <view class="price-data-content">
              <span class="price-icon">￥</span>
              <span class="price-number">{{ option.applyPrice }}</span>
            </view>
          </view>
        </view>
        <!-- #ifdef MP-WEIXIN || H5 || APP -->
        <view class="booking" @click="createOrder()">立即报名并支付</view>
        <!-- #endif -->
      </view>
    </view>
</template>

<script>
export default {
  data() {
    return {
      api: this.$config.baseApi,
      payWait: false,
      userInfo: null,
      option: {
        applyPrice: 0,
        nickname: '',
        phone: '',
        id: '',
        feeConfigId: '',
        couponId: 0,
        orderRemark: ''
      }
    };
  },
  
  onLoad(option) {
    // 保存传入的参数
    if (option) {
      this.option = {...this.option, ...option};
    }
    
    // 获取用户信息
    this.userInfo = uni.getStorageSync("userInfo");

    // 新增：通过接口获取报名费用
    uni.request({
      url: this.api + '/pay/fee/getApplyPrice', // 请替换为实际接口
      method: 'GET',
      data: {
        id: this.option.id // 或其他必要参数
      },
      success: (res) => {
        if (res.data.code === 0 && res.data.data) {
          this.option.applyPrice = res.data.data.applyPrice;
        }
      }
    });

  },
  
  onShow() {
    // 重置支付状态
    this.payWait = false;
  },
  
  methods: {
    // 检查用户是否登录
    checkUserLogin() {
      console.log(uni.getStorageSync("userInfo"));
      if(uni.getStorageSync("userInfo") === undefined || uni.getStorageSync("userInfo") === '') {
        uni.showToast({
          icon: "error",
          title: "您还没有登录!"
        });
        return false;
      }
      return true;
    },
    
    // 创建订单
    createOrder() {
      // 检查登录状态
      if (!this.checkUserLogin()) {
        uni.navigateTo({
          url: '/pages/auth/login/login'
        });
        return;
      }
      
      // 防止重复点击
      if (this.payWait) {
        return;
      }
      
      this.payWait = true;
      uni.showLoading({ title: '订单生成中' });
      
      const submitData = {
        applyName: this.option.nickname,
        applyPhone: this.option.phone,
        eventsId: this.option.id,
        feeConfigId: this.option.feeConfigId,
        couponId: this.option.couponId || 0,
        orderRemark: this.option.orderRemark || '',
        biscuitAmount: this.option.biscuitAmount || 0
      };
      
      uni.request({
        url: this.api + '/pay/order/createOrder',
        method: 'POST',
        data: submitData,
        success: (res) => {
          if (res.data.code === 0) {
            const data = {
              tableId: res.data.data.id
            };
            
            if (res.data.data.orderStatus === '0') {
              // 需要支付
              this.payJump(data);
            } else {
              // 无需支付，直接跳转结果页
              uni.hideLoading();
              uni.redirectTo({
                url: `/events/eventsResult?id=${res.data.data.id}`
              });
            }
          } else {
            this.payWait = false;
            uni.hideLoading();
            uni.showToast({ title: res.data.msg, duration: 2000, icon: 'none' });
          }
        },
        fail: (err) => {
          this.payWait = false;
          uni.hideLoading();
          console.error('创建订单失败', err);
          uni.showToast({ title: '创建订单失败，请重试', icon: 'none' });
        }
      });
    },
    
    // 发起支付
    payJump(order) {
      uni.request({
        url: this.api + '/app/order/pay',
        method: 'POST',
        data: {
          orderId: order.tableId,
          orderType: '0'
        },
        success: (res) => {
          this.payWait = false;
          if (res.data.code === 0) {
            // 调用支付API
            uni.requestPayment({
              ...res.data.data.signParam,
              success: () => {
                uni.redirectTo({
                  url: `/events/eventsResult?id=${order.tableId}`
                });
                uni.hideLoading();
              },
              fail: () => {
                uni.showToast({ title: '支付失败', icon: 'error' });
                uni.redirectTo({
                  url: '/user/order/index?orderStatus=1'
                });
                uni.hideLoading();
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({ title: res.data.msg || '支付请求失败', icon: 'none' });
          }
        },
        fail: (err) => {
          this.payWait = false;
          uni.hideLoading();
          console.error('支付请求失败', err);
          uni.showToast({ title: '支付请求失败', icon: 'none' });
        }
      });
    },
    
    // 跳转到登录页
    gotoLogin() {
      uni.navigateTo({
        url: '/pages/auth/login/login'
      });
    }
  }
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/minx.scss";
.booking_div_bar {
  position: fixed;
  bottom: 0;
  height: calc(56px + env(safe-area-inset-bottom));
  min-height: 70px;
  width: 100%;
  background-color: #ffffff;
  left: 0;
  padding: 14px 14px 0 14px;
  z-index: 98;

  .booking {
    width: 360rpx;
    float: right;
    height: 80rpx;
    background: #F0831E;
    border-radius: 1000px;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    line-height: 78rpx;
    position: relative;
    text-align: center;
    &::before{
      @include border(full, #414141, 1000px)
    }
  }
  .price-data {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
  }
  .price-data-title {
    font-size: 22rpx;
    color: #666;
  }
  .price-data-content {
   margin: 0 8rpx;
    font-size: 40rpx;
    font-weight: 500;
    color: #ee751e;
    align-items: flex-end;
    display: flex;
    .price-icon{
      font-size: 28rpx;
      line-height: 30rpx;
    }
    .price-number{
      font-size: 40rpx;
      line-height: 42rpx;
    }
  }
}
</style>
