export const IMGURL = 'https://img.domain.com'

const PLATFORM = import.meta.env.VITE_APP_BASE_URL

const URLO = {
  sprint: 'http://171.217.93.89:8604',
  pre: 'https://api.domain.net',
  prod: 'https://api.domain.com'
}
const BASEURL = URLO[PLATFORM]

const TOKEN = 'auth._token.local'
const OTHERUSERID = 'otherUserId'
// 保存默认头像
const SAVE_DEFAULT_AVATAR = 'save_default_avatar'
const SEARCH_TAGS = 'searchTags'
const SAVE_COMMON_IMAGE = 'save_common_image'
const SAVE_COMMON_PET_IMAGE_SHOW = 'save_pet_image_show'
const SAVE_LVPISHU_IMAGE_SHOW = 'save_lvpishu_image_show'

// 搜索存储
const SCIENCE_SEARCH_LIFE = 'science_search_life'
const SCIENCE_SEARCH_FOOD = 'science_search_food'
// 存储是否来源于app
const PLATEFORM_APP = 'plateform_app'
// 储存页面的cookie转为tokenstorage
const APP_STORAGE_COOKIE = 'app_store_token'
const PLATFORM_APP = 'platform'
const VERSION_APP = 'version_app'

// 刘海高度
const STATUS_BAR_HEIGHT = 'statusBarHeight'

// 宠物信息存储
const SAVE_PETS_DETAIL = 'savePetsDetail'
// 添加宠物时宠物品种信息 eg: { breedId: 1, breedName: '英短' }
const ADD_PETS_CATEGORY = 'addPetsCategory'

// 验证码状态
const VERIFY_CODE = {
  START: 0, // 开始
  GETTING: 1, // 获取ing
  AGAIN: 2 // 重新获取
}

// 腾讯im相关
const IM_KEY = {
  SDK_APP_ID: '1400813688',
  IM_ADMIN_INFO: 'IM_ADMIN_INFO', // IM管理员信息KEY，包含{userSig:签名，expirationTime:有效期}
  ADMIN_USER_ID: 'administrator', // 管理员账户userId
  SIGN_PAST_TIME: 7, // 签名有效期，单位天
  LOGIN_USER_SIG_INFO: 'LOGIN_USER_SIG_INFO' // 当前登录账户信息KEY，包含{userSig:签名，expirationTime:有效期}
}
// 注册来源
const REGISTER_FORM_MP = 'registerfrom'
const DELETE_PRODUCT_ID = 'deleteProductId'
// 首页banner弹出时间
const HOME_BANNER_POSTER_TIME = 'homeBannerPosterTime'
// 推荐用户id
const REGISTER_CUSTOMER_ID = 'registerCustomerId'
// 助力成功
const REGISTER_HELP_SUCCESS = 'registerHelpSuccess'
const CONTACT_INFO_X_PER = 'contactInfoPerson'
// 看宠物数量
const CAN_LOOK_PET_NUMBER = 'lookPetNumber'
// 报名成功后活动和订单信息
const ACTIVITY_ORDER_INFO = 'activityOrderInfo'
// 活动已添加宠物
const ACTIVITY_ADDED_PET_INFO = 'activityAddedPetInfo'
// 活动列表页显示活动
const SHOW_TAB_TYPE = 'showTabType'
const OPEN_ADD_PETS = 'openAddPets'
const GEENBOOK_REQUEST = 'geenbookRequest'
export {
  BASEURL,
  TOKEN,
  SAVE_DEFAULT_AVATAR,
  OTHERUSERID,
  SEARCH_TAGS,
  SCIENCE_SEARCH_LIFE,
  SCIENCE_SEARCH_FOOD,
  PLATEFORM_APP,
  APP_STORAGE_COOKIE,
  PLATFORM_APP,
  VERSION_APP,
  VERIFY_CODE,
  STATUS_BAR_HEIGHT,
  SAVE_COMMON_IMAGE,
  SAVE_COMMON_PET_IMAGE_SHOW,
  SAVE_LVPISHU_IMAGE_SHOW,
  SAVE_PETS_DETAIL,
  ADD_PETS_CATEGORY,
  IM_KEY,
  REGISTER_FORM_MP,
  DELETE_PRODUCT_ID,
  HOME_BANNER_POSTER_TIME,
  REGISTER_CUSTOMER_ID,
  REGISTER_HELP_SUCCESS,
  CONTACT_INFO_X_PER,
  CAN_LOOK_PET_NUMBER,
  ACTIVITY_ORDER_INFO,
  ACTIVITY_ADDED_PET_INFO,
  SHOW_TAB_TYPE,
  OPEN_ADD_PETS,
  GEENBOOK_REQUEST
}
