    $boxWidth:640rpx;
    $mainColor: #00aaff;
	.wrap{
		background-color: #f8f8f8;
	}
	.logoimg{
		width: 160rpx;
		height: 160rpx;
		display: none;
	}
	.placeholder{
		color: #DCDFE6;
	}
	.main-color{
		color: $mainColor;
	}
	.underline{
		text-decoration: underline;
	}
	.tabs{
		width: $boxWidth;
		height: 100rpx;
	}
	.btns{
		width: $boxWidth;
		margin-top: 80rpx;
	}
	.qbtn{
		height: 90rpx;
		border-radius: 10rpx;
		background-color: $mainColor;
		// background-image: linear-gradient(90deg, #65d3e8,#7dc3f2);
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
		box-shadow: 0 1px 0 2px rgba(0,0,0,0.05);
	}
	.btn-text-color{
		color: #ffffff;
	}
	.nav-text-color{
		color: #999999;
	}
	.label{
		width: 120rpx;
		// display: none;
	}
	.label_icon{
		width: 38rpx;
		height: 38rpx;
		display: none;
		margin-right: 30rpx;
	}
	.label_fgs{
		height: 36rpx;
		width: 1px;
		border-left:1px solid #E5E5E5;
		margin-right: 30rpx;
		display: none;
	}
  .topbox{
	  width: 100%;
	  height: 400rpx;
	  background-color: $mainColor;
	
	/* #ifdef MP-WEIXIN */
	background: url("http://img1.baidu.com/it/u=145454631,654410765&fm=253&app=138&f=JPEG?w=465&h=838");
	/* #endif */
	 
	  background-repeat: no-repeat;
	  background-size: 100%;
	  
  }
  .form{
	  width: $boxWidth;
  }
  .form-item{
	  padding: 30rpx 30rpx;
	  margin-left: -60rpx;
	  margin-right: -60rpx;
	  border-bottom: 1px solid #eee;
	  background-color: #ffffff;
  }
  .active{
	  position: relative;
  }
  .active::after{
	  content: "";
	  position: absolute;
	  bottom:0;
	  width: 60rpx;
	  height: 8rpx;
	  border-radius: 4rpx;
	  background-color: $mainColor;
  }
  .curtext{
	  color: $mainColor;
  }
  .yzm{
	  background-color: $mainColor;
	  color: #ffffff;
	  padding-left: 20rpx;
	  padding-right: 20rpx;
	  border-radius: 10rpx;
  }