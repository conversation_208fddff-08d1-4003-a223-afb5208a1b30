// 全局配置文件
// 根据环境选择API地址
const ENV = import.meta.env.MODE || 'production';

let baseApi = '';
let appName = '驾考系统';

// #ifdef H5
// if (import.meta.env.MODE === 'development') {
//   baseApi = 'http://8.130.145.134:8080';
//   appName = '驾考系统(H5开发)';
// } else if (import.meta.env.MODE === 'test') {
//   baseApi = 'http://8.130.145.134:8080';
//   appName = '驾考系统(H5测试)';
// } else {
//   baseApi = 'https://www.xzys-mtczy-test.com';
//   appName = '驾考系统(H5生产)';
// }
// #endif

// #ifdef MP-WEIXIN
if (import.meta.env.MODE === 'development') {
  baseApi = 'https://www.xzys-mtczy-test.com';
  appName = '驾考系统(小程序开发)';
} else if (import.meta.env.MODE === 'test') {
  baseApi = 'https://www.xzys-mtczy-test.com';
  appName = '驾考系统(小程序测试)';
} else {
  baseApi = 'https://www.xzys-mtczy-test.com';
  appName = '驾考系统(小程序生产)';
}
// #endif

const config = {
  baseApi,
  appName
};

// 导出对应环境的配置，如果没有找到对应环境则使用生产环境配置
export default config;//[ENV] || config.production;