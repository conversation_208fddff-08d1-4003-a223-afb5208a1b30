<template>
    <view class="login-box">
      <view class="main-box">
        <view>
          <!-- <img src="https://img.qumoyugo.com/webimgbg/pk_logo.png"  class="img" alt=""> -->
        </view>
        <view class="login-com">
          <button type="default" v-if="checked" open-type="getPhoneNumber" @getphonenumber="getphonenumber" class="btn">
            手机号快捷登录
          </button>
          <view type="default" v-else @click="handlerTips" class="btn">
            手机号快捷登录
          </view>
        </view>
      </view>
      <view class="bottom">
        <!-- <label class="radio">
          <radio style="transform:scale(0.7)" :checked="checked" color="#000000" />
        </label> -->
        <label class="radio" @click="handlerClickRadio">
          <uni-icons :type="checked ? 'checkbox-filled':'circle'" size="20"></uni-icons>
        </label>
        <view>
          <text @click="checked = !checked">我已阅读并同意</text>
          <text @click="gotoPage(1)" style="color:#000000">《用户协议》</text>和
          <text @click="gotoPage(2)" style="color:#000000">《隐私保护协议》</text>
        </view>
      </view>
    </view>
    <!-- <SelfToast content="请阅读并勾选下方《品客聚精彩用户协议》和《隐私保护协议》" ref="selfToast"></SelfToast> -->
    <view class="u-center-popupRef" v-if="popupRef">
      <view class="popup-toast">
        <view class="title">提示</view>
        <view class="">请阅读并勾选下方《用户协议》和《隐私保护协议》</view>
      </view>
    </view>
    <template v-if="PLATFORM !== 'prod'">
      <view style="padding: 54rpx">
        <uni-easyinput trim="all" v-model="telNumber" placeholder="输入手机号"></uni-easyinput>
        <button @click="btnLogin" style="margin-top:40rpx" type="primary">注册</button>
      </view>
    </template>
  </template>
  
  <script>

// import { useStore } from '@/store' // Options API 通常通过 this.$store 访问
// import { storage, urlGet } from '@/utils/utils'
// import { TOKEN, REGISTER_FORM_MP, REGISTER_CUSTOMER_ID, REGISTER_HELP_SUCCESS, OPEN_ADD_PETS } from '@/assets/js/config.js'
// import { loginIM } from '@/assets/js/tencentIM/tencentIM.js' // Keep commented if not used

import UniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';

export default {
      // 添加 components 选项来注册导入的组件
  components: {
    UniEasyinput // 或者 'uni-easyinput': UniEasyinput
  },
  data() {
    return {
      // 使用 this.$config 或直接访问环境变量 (取决于您的项目配置方式)
      // 假设 $config.platform 存在，类似 index.vue 的 this.$config.baseApi
      // 如果没有，可以使用 import.meta.env
      PLATFORM: import.meta.env.VITE_APP_BASE_URL, // 或者 this.$config?.platform
      checked: false,
    //   timer: null, // 注意：此 timer 在逻辑中未被使用
      timerLogin: null,
      popupRef: false,
      redirectUrl: '',
      telNumber: null // 用于非生产环境测试输入
    }
  },
  // onLoad 生命周期钩子
  onLoad(option) {
    console.log('Login onLoad options:', option); // 调试信息
    if (option && option.redirect) { // 检查 option 是否存在
      try {
        // 解码可能存在的双重编码 URL
        let decoded = decodeURIComponent(option.redirect);
        // 检查是否需要再次解码
        if (decoded.includes('%')) {
            decoded = decodeURIComponent(decoded);
        }
        this.redirectUrl = decoded;
        console.log('解析后的 redirectUrl:', this.redirectUrl);
      } catch (e) {
        console.error('解码 redirect URL 失败:', e);
        this.redirectUrl = '/pages/index'; // 设置回退 URL
      }
    } else {
      this.redirectUrl = '/pages/index'; // 如果没有提供 redirect，设置默认值
    }
    console.log('最终 redirectUrl:', this.redirectUrl); // 确认最终值
  },
  methods: {
    gotoPage(value) {
      uni.navigateTo({
        url: value === 2 ? '/pages/privacy' : '/pages/concert'
      })
    },

    handlerClickRadio() {
      this.checked = !this.checked
    },

    handlerTips() {
      this.popupRef = true
      // 清除之前的定时器（如果存在）
      if (this.timerLogin) {
        clearTimeout(this.timerLogin)
      }
      this.timerLogin = setTimeout(() => {
        this.popupRef = false
      }, 2000)
    },

    getphonenumber(e) {
      console.log('getphonenumber event:', e); // 调试信息
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        console.warn('用户拒绝授权手机号:', e.detail.errMsg)
        // 可以选择性地给用户提示
        // uni.showToast({ title: '需要授权手机号才能登录', icon: 'none' });
        return false
      }
      // 确保 detail 中有 code
      if (!e.detail.code) {
        console.error('获取手机号 code 失败:', e.detail);
        uni.showModal({
          title: '提示',
          content: '获取手机号信息失败，请稍后重试或检查微信版本。',
          showCancel: false
        })
        return false
      }

      // 使用 wx.login 获取用户的登录 code 用于会话交换
      wx.login({
        success: (loginRes) => {
          console.log('wx.login success:', loginRes); // 调试信息
          if (!loginRes.code) {
            uni.showToast({ title: '获取登录凭证失败', icon: 'none' });
            return;
          }

          uni.showLoading({
            title: '登录中...'
          })

          // 1. 交换 wx.login code 获取 openid/session_key
          this.$https({ // 使用 this.$https
            url: '/js-token', // 后端交换 code 的端点
            method: 'POST', // 或 GET，取决于您的后端
            data: {
              code: loginRes.code,
              scene: 'mp4' // 或您特定的场景标识符
            }
          }).then(resOpenid => {
            console.log('/js-token response:', resOpenid); // 调试信息
            if (resOpenid.code === 0 && resOpenid.data.openid) {
              const openId = resOpenid.data.openid;
              // const sessionKey = resOpenid.data.session_key; // 如果在前端解密则需要

              // 准备使用手机号 code 和 openid 的登录数据
              const registerFrom = storage.get(REGISTER_FORM_MP)
              const recommendCustomerId = storage.get(REGISTER_CUSTOMER_ID)
              storage.remove(REGISTER_CUSTOMER_ID) // 获取后移除
              storage.remove(REGISTER_FORM_MP)    // 获取后移除

              const loginData = {
                code: e.detail.code, // 这是手机号的 code
                type: 'wechat',
                registerPhoneType: 'MP',
                scene: 'mp4',
                openId: openId, // 传递获取到的 openId
                // sessionKey: sessionKey, // 仅当后端需要时传递
                recommendCustomerId: recommendCustomerId || null // 确保未定义时为 null
              }

              // 根据 redirectUrl 添加可选参数
              const redirect = this.redirectUrl; // 使用局部变量以便清晰
              if (redirect.includes('/events/eventsDetail') && urlGet('share', redirect)) {
                loginData.relationActivityId = urlGet('id', redirect)
              }
              const entryType = urlGet('entryType', redirect);
              if (entryType) {
                loginData.entryType = entryType;
              }
              if (registerFrom) {
                loginData.registerFrom = registerFrom
              }
              const relationActivityId = urlGet('relationActivityId', redirect);
              if (relationActivityId) {
                loginData.relationActivityId = relationActivityId; // 如果两者都存在则覆盖之前的
              }
              const activityOwnerId = urlGet('activityOwnerId', redirect);
              if (activityOwnerId) {
                loginData.activityOwnerId = activityOwnerId;
              }

              console.log('Login data:', loginData) // 调试信息

              // 2. 调用实际的登录端点
              this.$https({
                url: '/login',
                method: 'POST',
                data: loginData
              }).then(res => {
                console.log('/login response:', res); // 调试信息
                uni.hideLoading() // 隐藏加载指示器
                if (res.code === 0 && res.data.token) {
                  // 登录成功
                  storage.remove(REGISTER_FORM_MP) // 以防万一再次清理存储
                  this.$store.commit('setToken', res.data.token) // 使用 this.$store
                  storage.set(TOKEN, res.data.token)

                  if (entryType === '2') { // 再次检查 entryType 以执行特定逻辑
                    storage.set(OPEN_ADD_PETS, true)
                  }

                  let targetUrl = ''
                  if (res.data.hasCompleted) {
                    targetUrl = decodeURIComponent(redirect) // 使用原始重定向 URL
                  } else {
                    // 重定向到资料完善页面，传递原始重定向地址
                    targetUrl = `/user/uploadAvatar?redirect=${encodeURIComponent(redirect)}`
                  }
                  console.log('跳转url链接1', targetUrl)

                  // 处理 eventsDetail 重定向的特定逻辑
                  if (targetUrl.includes('/events/eventsDetail') && urlGet('share', targetUrl) && +urlGet('rmd', targetUrl) !== res.data.customerId) {
                    storage.set(REGISTER_HELP_SUCCESS, 1)
                  }

                  // 确保 targetUrl 有效且不是登录页面本身
                  if (!targetUrl || targetUrl.includes('/login') || targetUrl === '/') {
                    targetUrl = '/pages/index' // 默认为首页
                  }
                  console.log('跳转url链接2', targetUrl)

                  // 执行重定向
                  uni.redirectTo({
                    url: targetUrl,
                    fail: (err) => {
                      console.warn(`redirectTo ${targetUrl} 失败, 尝试 switchTab:`, err)
                      // 如果 redirectTo 失败（例如目标是 tab 页面），则回退到 switchTab
                      uni.switchTab({
                        url: targetUrl,
                        fail: (switchErr) => {
                           console.error(`switchTab ${targetUrl} 也失败了:`, switchErr);
                           // 如果 switchTab 也失败，则最终回退到首页
                           uni.reLaunch({ url: '/pages/index' });
                        }
                      })
                    }
                  })

                  // 登录成功并设置重定向后获取个人资料
                  this.$https({
                    url: '/profile',
                    method: 'GET' // 通常个人资料使用 GET
                  }).then(profile => {
                    console.log('/profile response:', profile); // 调试信息
                    if (profile.code === 0) {
                      this.$store.commit('setProfile', profile.data) // 使用 this.$store
                      // --- IM 登录 (如果需要) ---
                      // const phone = profile.data.attr?.phone;
                      // const faceUrl = profile.data.attr?.faceUrl;
                      // const nickname = profile.data.attr?.nickname;
                      // if (phone && faceUrl && nickname) {
                      //   console.log('准备登录IM:', phone, faceUrl, nickname);
                      //   // loginIM(phone, faceUrl, nickname);
                      // } else {
                      //   console.warn('IM登录所需信息不完整:', profile.data.attr);
                      // }
                      // --- 结束 IM 登录 ---
                    } else {
                      console.warn('获取用户信息失败:', profile.message);
                    }
                  }).catch(profileErr => {
                     console.error('请求用户信息接口时出错:', profileErr);
                  });

                } else {
                  // 登录失败
                  uni.showModal({
                    title: '登录失败',
                    content: res.message || '无法登录，请稍后重试。',
                    showCancel: false
                  })
                }
              }).catch(loginErr => {
                uni.hideLoading()
                console.error('登录请求失败:', loginErr)
                uni.showModal({
                  title: '网络错误',
                  content: '登录请求失败，请检查网络连接。',
                  showCancel: false
                })
              })

            } else {
              uni.hideLoading()
              console.error('换取 openid 失败:', resOpenid)
              uni.showModal({
                title: '登录准备失败',
                content: resOpenid.message || '无法获取用户信息凭证，请重试。',
                showCancel: false
              })
            }
          }).catch(openidErr => {
            uni.hideLoading()
            console.error('请求 /js-token 接口时出错:', openidErr)
            uni.showModal({
              title: '网络错误',
              content: '无法连接服务器进行登录准备，请检查网络。',
              showCancel: false
            })
          })
        },
        fail: (loginErr) => {
          uni.hideLoading(); // 确保失败时隐藏加载
          console.error('wx.login 调用失败:', loginErr);
          uni.showToast({ title: '微信登录接口调用失败', icon: 'none' });
        }
      })
    },

    // --- 非生产环境测试登录 ---
    btnLogin() {
      if (!this.telNumber) {
          uni.showToast({ title: '请输入手机号', icon: 'none' });
          return;
      }
      if (!/^\d{11}$/.test(this.telNumber)) {
          uni.showToast({ title: '请输入有效的11位手机号', icon: 'none' });
          return;
      }

      const recommendCustomerId = storage.get(REGISTER_CUSTOMER_ID)
      storage.remove(REGISTER_CUSTOMER_ID) // 获取后移除

      const data = {
        account: `86-${this.telNumber}`, // 假设后端期望此格式
        code: this.PLATFORM === 'sprint' ? '7777' : '0832', // 测试码
        type: 'phone', // 登录类型
        registerPhoneType: 'MP', // 假设注册类型相同
        scene: 'mp4', // 假设场景相同
        entryType: urlGet('entryType', this.redirectUrl),
        relationActivityId: urlGet('relationActivityId', this.redirectUrl) || urlGet('id', this.redirectUrl),
        activityOwnerId: urlGet('activityOwnerId', this.redirectUrl)
      }
      if (recommendCustomerId) {
        data.recommendCustomerId = recommendCustomerId
      }

      uni.showLoading({ title: '登录中...' });

      this.$https({
        url: '/login',
        method: 'POST',
        data
      }).then(res => {
        uni.hideLoading();
        if (res.code === 0 && res.data.token) {
          // 登录成功
          this.$store.commit('setToken', res.data.token)
          storage.set(TOKEN, res.data.token)
          storage.set(OPEN_ADD_PETS, true) // 假设测试登录总是设置此项

          let targetUrl = ''
          const redirect = this.redirectUrl;
          if (res.data.hasCompleted) {
            targetUrl = decodeURIComponent(redirect)
          } else {
            targetUrl = `/user/uploadAvatar?redirect=${encodeURIComponent(redirect)}`
          }
          console.log('跳转url链接1', targetUrl)

          if (targetUrl.includes('/events/eventsDetail') && urlGet('share', targetUrl) && +urlGet('rmd', targetUrl) !== res.data.customerId) {
            storage.set(REGISTER_HELP_SUCCESS, 1)
          }

          if (!targetUrl || targetUrl.includes('/login') || targetUrl === '/') {
            targetUrl = '/pages/index'
          }
          console.log('跳转url链接2', targetUrl)

          uni.redirectTo({
            url: targetUrl,
            fail: () => {
              uni.switchTab({
                url: targetUrl,
                 fail: () => uni.reLaunch({ url: '/pages/index' })
              })
            }
          })

          // 获取个人资料
          this.$https({
            url: '/profile',
            method: 'GET'
          }).then(profile => {
            if (profile.code === 0) {
              this.$store.commit('setProfile', profile.data)
              // 可选的 IM 登录逻辑在此处
            }
          }).catch(profileErr => {
             console.error('请求用户信息接口时出错:', profileErr);
          });

        } else {
          uni.showModal({
            title: '登录失败',
            content: res.message || '测试登录失败，请检查。',
            showCancel: false
          })
        }
      }).catch(err => {
        uni.hideLoading()
        console.error('测试登录请求失败:', err)
        uni.showModal({
          title: '网络错误',
          content: '测试登录请求失败，请检查网络。',
          showCancel: false
        })
      })
    }
  },
  // beforeUnmount 生命周期钩子 (Vue 3) 或 beforeDestroy (Vue 2)
  // uniapp 中两者皆可，但 beforeUnmount 更符合 Vue 3 习惯
  beforeUnmount() {
    // 清理定时器
    if (this.timer) { // 虽然 timer 未使用，但以防万一
        clearTimeout(this.timer);
        this.timer = null;
    }
    if (this.timerLogin) {
      clearTimeout(this.timerLogin)
      this.timerLogin = null
    }
  }
}
</script>

  
  <style scoped lang="scss">
  .login-box{
    padding: 64rpx 15rpx;
    height: 100vh;
    .main-box{
      .img{
        width: 718rpx;
        height: 546rpx;
      }
      .login-com{
        margin: 0 72rpx;
        .btn{
          display: flex;
          justify-content: center;
          align-items: center;
          height: 84rpx;
          border-radius: 42rpx;
          background: #58AA46;
          color: #ffffff;
          margin-top: 90rpx;
          font-size: 32rpx;
          &:active{
            opacity: 0.8;
          }
        }
      }
    }
    .bottom{
      color: #999999;
      display: flex;
      margin: 40rpx 60rpx;
      font-size: 26rpx;
    }
  }
  .u-center-popupRef{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .popup-toast{
    width: 520rpx;
    padding: 20rpx 50rpx;
    background: rgba(0,0,0,0.8);
    border-radius: 12rpx;
    text-align: center;
    font-size: 28rpx;
    color: #ffffff;
    .title{
      font-size: 30rpx;
      height: 42rpx;
      line-height: 42rpx;
      margin-bottom: 14rpx;
    }
  }
  </style>