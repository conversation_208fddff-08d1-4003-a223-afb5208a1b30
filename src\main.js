import {
	createSSRApp
} from "vue";
import App from "./App.vue";

//正则表达式
import validate from '@/js_sdk/fshjie-formvalidate/ys-validate.js'
// 导入全局配置
import config from '@/common/config.js'  // 修改为相对路径

export function createApp() {
	const app = createSSRApp(App);
	
	// 在Vue 3中，使用app.config.globalProperties替代Vue.prototype
	app.config.globalProperties.$validate = validate;
	// 将配置挂载到全局
	app.config.globalProperties.$config = config;
	
	return {
		app,
	};
}