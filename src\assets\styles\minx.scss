@mixin border($position: full, $borderColor: #ddd, $radius: 0) {
  content: '';
  position: absolute;
  z-index: 1;
  pointer-events: none;

  @if $position==top {
    height: 1px;
    left: 0;
    right: 0;
    top: 0;
    background-color: $borderColor;
    @media only screen and (-webkit-min-device-pixel-ratio:2) {
      & {
        -webkit-transform: scaleY(0.5);
        -webkit-transform-origin: 50% 0%;
      }
    }
  }

  @if $position==bottom {
    height: 1px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $borderColor;
    @media only screen and (-webkit-min-device-pixel-ratio:2) {
      & {
        -webkit-transform: scaleY(0.5);
        -webkit-transform-origin: 50% 100%;
      }
    }
  }

  @if $position==left {
    width: 1px;
    top: 0;
    bottom: 0;
    left: 0;
    background-color: $borderColor;
    @media only screen and (-webkit-min-device-pixel-ratio:2) {
      & {
        -webkit-transform: scaleX(0.5);
        -webkit-transform-origin: 0% 50%;
      }
    }
  }

  @if $position==right {
    width: 1px;
    top: 0;
    bottom: 0;
    right: 0;
    background-color: $borderColor;
    @media only screen and (-webkit-min-device-pixel-ratio:2) {
      & {
        -webkit-transform: scaleX(0.5);
        -webkit-transform-origin: 100% 50%;
      }
    }
  }

  @if $position==full {
    border: 1px solid $borderColor;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    // background: none;
    // border-color: $borderColor;

    @if $radius !=0 {
      border-radius: $radius;
    }

    @media only screen and (-webkit-min-device-pixel-ratio:2) {
      & {
        right: -100%;
        bottom: -100%;
        -webkit-transform: scale(0.5);
        -webkit-transform-origin: 0% 0%;
        $radiusx2: null;

        @each $i in $radius {
          $radiusx2: append($radiusx2, $i * 2);
        }

        @if $radius !=0 {
          border-radius: $radiusx2;
        }
      }
    }
  }
}