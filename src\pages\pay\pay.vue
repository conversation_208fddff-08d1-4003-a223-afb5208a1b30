<template>
    <view class="booking_div_bar">
      <view class="price-data">
        <view>
          <view style="display:flex;height:42rpx;align-items: flex-end;">
            <view class="price-data-title">报名费用</view>
            <view class="price-data-content">
              <span class="price-icon">￥</span>
              <span class="price-number">{{ option.applyPrice }}</span>
            </view>
          </view>
        </view>
        <!-- #ifdef MP-WEIXIN || H5 || APP -->
        <button type="primary" @click="onApplyAndPay">立即报名并支付</button>
        <!-- #endif -->
      </view>
    </view>
</template>

<script setup lang="ts">
//import { jsapiAPI, closeOrderAPI } from '../../service/pay';
import { ref } from 'vue';
const { proxy } = getCurrentInstance();

const option = ref({
	applyPrice: 0,
	nickname: '',
	phone: '',
	id: '',
	feeConfigId: '',
	couponId: 0,
	orderRemark: ''
});
const payWait = ref(false);

// 报名+支付一体化
const onApplyAndPay = async () => {
	if (!option.value.nickname || !option.value.phone) {
		uni.showToast({ title: '请填写完整信息', icon: 'none' });
		return;
	}
	if (payWait.value) return;
	payWait.value = true;
	uni.showLoading({ title: '订单生成中' });

	// 1. 创建订单
	uni.request({
		url: proxy.$config.baseApi + '/pay/order/createOrder',
		method: 'POST',
		data: {
			applyName: option.value.nickname,
			applyPhone: option.value.phone,
			eventsId: option.value.id,
			feeConfigId: option.value.feeConfigId,
			couponId: option.value.couponId || 0,
			orderRemark: option.value.orderRemark || ''
		},
		success: (res) => {
			if (res.data.code === 0) {
				const orderId = res.data.data.id;
				if (res.data.data.orderStatus === '0') {
					// 2. 需要支付
					payOrder(orderId);
				} else {
					// 3. 无需支付，直接跳转
					uni.hideLoading();
					uni.redirectTo({
						url: `/events/eventsResult?id=${orderId}`
					});
				}
			} else {
				payWait.value = false;
				uni.hideLoading();
				uni.showToast({ title: res.data.msg, icon: 'none' });
			}
		},
		fail: () => {
			payWait.value = false;
			uni.hideLoading();
			uni.showToast({ title: '创建订单失败', icon: 'none' });
		}
	});
};

const payOrder = (orderId: string) => {
	uni.request({
		url: proxy.$config.baseApi + '/app/order/pay',
		method: 'POST',
		data: {
			orderId,
			orderType: '0'
		},
		success: (res) => {
			payWait.value = false;
			if (res.data.code === 0) {
				uni.requestPayment({
					...res.data.data.signParam,
					success: () => {
						uni.redirectTo({
							url: `/events/eventsResult?id=${orderId}`
						});
						uni.hideLoading();
					},
					fail: () => {
						uni.showToast({ title: '支付失败', icon: 'error' });
						uni.redirectTo({
							url: '/user/order/index?orderStatus=1'
						});
						uni.hideLoading();
					}
				});
			} else {
				uni.hideLoading();
				uni.showToast({ title: res.data.msg || '支付请求失败', icon: 'none' });
			}
		},
		fail: () => {
			payWait.value = false;
			uni.hideLoading();
			uni.showToast({ title: '支付请求失败', icon: 'none' });
		}
	});
};
</script>
<style scoped lang="scss">
@import "@/assets/styles/minx.scss";
.booking_div_bar {
  position: fixed;
  bottom: 0;
  height: calc(56px + env(safe-area-inset-bottom));
  min-height: 70px;
  width: 100%;
  background-color: #ffffff;
  left: 0;
  padding: 14px 14px 0 14px;
  z-index: 98;

  .booking {
    width: 360rpx;
    float: right;
    height: 80rpx;
    background: #F0831E;
    border-radius: 1000px;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    line-height: 78rpx;
    position: relative;
    text-align: center;
    &::before{
      @include border(full, #414141, 1000px)
    }
  }
  .price-data {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
  }
  .price-data-title {
    font-size: 22rpx;
    color: #666;
  }
  .price-data-content {
   margin: 0 8rpx;
    font-size: 40rpx;
    font-weight: 500;
    color: #ee751e;
    align-items: flex-end;
    display: flex;
    .price-icon{
      font-size: 28rpx;
      line-height: 30rpx;
    }
    .price-number{
      font-size: 40rpx;
      line-height: 42rpx;
    }
  }
}
</style>
