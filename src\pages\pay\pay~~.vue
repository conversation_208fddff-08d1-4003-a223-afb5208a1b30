<template>
	<view class="content">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<button type="primary" @click="onPay">微信支付</button>

		</view>
	</view>
	<!-- 接口请求加载动画 -->
	<!-- <loading></loading> -->
</template>

<script setup lang="ts">
	import { jsapiAPI,closeOrderAPI } from '../../service/pay';
	import type { PrepayParams, PayParams } from '../../types/pay';
	import { getCurrentInstance } from 'vue';
	const { proxy } = getCurrentInstance();

	// 新增：用户信息
	// const userInfo = ref<any>(null);

	// onMounted(() => {
	// 	userInfo.value = uni.getStorageSync("userInfo");
	// });

	//关单
	const onCloseOrder = async() => {
		const res = await closeOrderAPI();
	}


	//支付
	const onPay = async () => {
	console.log("get PrepayId");
	uni.login({
		provider: 'weixin',
		success: function(loginRes) {
			if (!loginRes.code) {
				uni.showToast({ title: '微信登录失败', icon: 'none' });
				return;
			}
			console.log('loginRes.code:', loginRes.code); // 新增打印
			// 新增：获取本地 userInfo
			const localUserInfo = uni.getStorageSync("userInfo");
			console.log('phone', localUserInfo.uPhone);
			uni.request({
				url: proxy.$config.baseApi + '/api/getUserLoginByApplets',
				method: 'POST',
				// 新增：将 userInfo（如 pkId/userId）一并传递给后端
				data: { 
					code: loginRes.code,
					phone: localUserInfo.uPhone //|| localUserInfo?.uId // 根据你的 userInfo 字段名调整
				},
				success: (res) => {
					const resData = res.data;
					console.log('返回openid和用户信息loginRes.code:', resData);
					// 兼容后端返回openid和用户信息
					const openid = resData.uWxid || (resData.data && resData.data.openid);
					if (openid) {
						// 如果有用户信息，保存到本地
						// if (resData.pkId || (resData.data && resData.data.pkId)) {
							uni.setStorageSync("userInfo", resData.data || resData);
						// }
						const preOrder = {
							name: '测试充值',
							totalFee: 1,
							openid: openid
						} as PrepayParams;
						jsapiAPI(preOrder).then(payRes => {
							console.log('----------------res---------------');
							console.log(payRes);
							if (!payRes || !payRes.timeStamp) {
								uni.showToast({ title: '支付参数异常', icon: 'none' });
								return;
							}
							callWeChatPay(payRes as PayParams);
						});
					} else {
						uni.showToast({ title: '获取openid失败', icon: 'none' });
					}
				},
				fail: () => {
					uni.showToast({ title: '微信登录接口失败', icon: 'none' });
				}
			});
		},
		fail: function () {
			uni.showToast({ title: '微信授权失败', icon: 'none' });
		}
	});
}

	const callWeChatPay = async (payParams:PayParams)=>{
      console.log(payParams);
	  
      //下述传参要求，请看微信官方文档中的《小程序支付/API列表/小程序调起支付》
      uni.requestPayment({
            timeStamp: payParams.timeStamp,
            nonceStr: payParams.nonceStr,
            package: payParams.package,
            signType:'RSA',
            paySign:payParams.paySign,
            success(res) {
                uni.showToast({
                    title: '支付成功',
                    icon: 'success'
                });
                console.log('支付成功:', res);
    // 跳转到 tabbar 页面必须用 switchTab
    uni.switchTab({
        url: '/pages/home/<USER>'
    });
            },
            fail(err) {
                uni.showToast({
                    title: '支付失败',
                    icon: 'none'
                });
                console.error('支付失败:', err);
            }
        });
    }
	

</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>