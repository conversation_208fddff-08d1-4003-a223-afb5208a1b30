    $boxWidth:640rpx;
    $mainColor: #f73131;
	.logoimg{
		width: 160rpx;
		height: 160rpx;
	}
	.placeholder{
		color: #DCDFE6;
	}
	.main-color{
		color: $mainColor;
	}
	.underline{
		text-decoration: underline;
	}
	.tabs{
		width: $boxWidth;
		height: 100rpx;
	}
	.btns{
		width: $boxWidth;
		margin-top: 80rpx;
	}
	.qbtn{
		height: 90rpx;
		border-radius: 50rpx;
		background-color: $mainColor;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
		box-shadow: 0 1px 0 2px rgba(0,0,0,0.05);
	}
	.btn-text-color{
		color: #ffffff;
	}
	.nav-text-color{
		color: #999999;
	}
	.label{
		width: 120rpx;
		display: none;
	}
	.label_icon{
		width: 38rpx;
		height: 38rpx;
		margin-right: 30rpx;
		display: none;
	}
	.label_fgs{
		height: 36rpx;
		width: 1px;
		border-left:1px solid #E5E5E5;
		margin-right: 30rpx;
		display: none;
	}
  .topbox{
	   width: 750rpx;
	  height: 400rpx;
  }
  .form{
	  width: $boxWidth;
  }
  .form-item{
	  padding: 30rpx 0;
	  border-bottom: 1px solid #eee;
  }
  .active{
	  position: relative;
  }
  .active::after{
	  content: "";
	  position: absolute;
	  bottom:0;
	  width: 60rpx;
	  height: 8rpx;
	  border-radius: 4rpx;
	  background-color: $mainColor;
  }
  .curtext{
	  color: $mainColor;
  }