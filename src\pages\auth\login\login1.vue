<template>
	<!-- ... existing template code ... -->
		<view class="btns" style="margin-top: 30rpx;">
			<view class="qbtn" @click="doLogin" v-if="tabIndex==0" >
				<text class="btn-text-color fs30">登录</text>
			</view>
			<view v-if="tabIndex==1" @click="Register" class="qbtn">
				<text class="btn-text-color fs30">注册</text>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<!-- 添加微信一键登录按钮 -->
			<view class="qbtn margin-top" @click="wxLogin">
				<image class="wxLogo" style="width: 40rpx; height: 40rpx; vertical-align: middle; margin-right: 10rpx;" src="/static/icon/wxLogo.png" mode="aspectFill"></image>
				<text class="btn-text-color fs30">微信一键登录</text>
			</view>
			<!-- #endif -->

			<view class="flex ptb30 mlr20 space-between" v-if="tabIndex ==0">
				<!-- ... existing code ... -->
			</view>
		</view>

</template>

<script>
	// ... existing imports ...

	export default {
		data() {
			return {
				// ... existing data properties ...
				api: this.$config.baseApi,
				password: true,
				tabIndex: 0,
				userinfo:null,
				checkCode:'1234',
				loginForm:{
					code:null,
					uNumber:null,
					uPassword:null
				},
				registerForm:{
					code:null,
					uNumberok:false,
					uName:null,
					uNumber:null,
					uPassword:null,
					uPassword2:null,
					uPhone:null,
					phoneCode:null,
					btnText:'发送'
				}
			}
		},
		onLoad(){
			this.checkCode=this.getCode(4);
		},
		methods: {
            // ... existing methods (sendPhoneCode, getCode, checkPhoneNumberIsRegister, doLogin, checkUNumber, tab, goLogin, goRegister, Register, goForget) ...

            /**
             * 微信一键登录
             * 参考 login~.vue 的逻辑，并结合 uni.getUserProfile 获取用户信息
             */
            wxLogin() {
                let that = this;
                uni.showLoading({
                    title: '正在登录...'
                });

                // 1. 调用 uni.login 获取 code
                uni.login({
                    provider: 'weixin',
                    success: function(loginRes) {
                        console.log('uni.login 成功，code:', loginRes.code);
                        if (loginRes.code) {
                            // 2. 调用 uni.getUserProfile 获取用户信息 (头像、昵称)
                            uni.getUserProfile({
                                desc: '用于完善会员资料', // 声明获取用户个人信息后的用途
                                lang: 'zh_CN',
                                success: (infoRes) => {
                                    console.log('uni.getUserProfile 成功:', infoRes);
                                    // 3. 将 code 和用户信息发送到后端
                                    uni.request({
                                        // 使用 data 中定义的 api 地址，并指定后端接口路径
                                        url: that.api + "/api/wxLogin/user/getUserLoginByApplets", // 请确保后端有此接口处理微信登录
                                        method: "POST",
                                        data: {
                                            code: loginRes.code,
                                            // 传递从 getUserProfile 获取的用户信息
                                            nickName: infoRes.userInfo.nickName,
                                            avatarUrl: infoRes.userInfo.avatarUrl,
                                            gender: infoRes.userInfo.gender // 0:未知, 1:男, 2:女
                                            // 如果需要其他信息，如 encryptedData, iv 用于解密手机号，也在此传递
                                        },
                                        success(res) {
                                            uni.hideLoading();
                                            console.log('后端微信登录响应:', res.data);
                                            // 根据后端返回结果进行处理
                                            if (res.data && res.data.code === 200 && res.data.data) { // 假设后端成功返回格式 {code: 200, message: '...', data: {userInfo, token}}
                                                // 登录成功
                                                uni.setStorageSync("userInfo", res.data.data.userInfo); // 保存用户信息
                                                uni.setStorageSync("token", res.data.data.token); // 保存登录凭证 Token
                                                uni.showToast({
                                                    title: '登录成功',
                                                    icon: 'success'
                                                });
                                                // 跳转到首页或个人中心
                                                uni.reLaunch({
                                                    url: "/pages/mine/index" // 跳转到个人中心页面
                                                });
                                            } else {
                                                // 后端处理失败或返回错误码
                                                uni.showToast({
                                                    title: res.data.message || '微信登录失败，请稍后重试',
                                                    icon: 'none'
                                                });
                                            }
                                        },
                                        fail(err) {
                                            uni.hideLoading();
                                            console.error('后端微信登录请求失败:', err);
                                            uni.showToast({
                                                title: '网络错误，微信登录失败',
                                                icon: 'none'
                                            });
                                        }
                                    });
                                },
                                fail(err) {
                                    uni.hideLoading();
                                    console.error('uni.getUserProfile 失败:', err);
                                    // 用户拒绝授权或其他错误
                                    uni.showToast({
                                        title: '获取用户信息失败',
                                        icon: 'none'
                                    });
                                }
                            });
                        } else {
                            uni.hideLoading();
                            console.error('uni.login 获取 code 失败:', loginRes.errMsg);
                            uni.showToast({
                                title: '微信登录失败，无法获取凭证',
                                icon: 'none'
                            });
                        }
                    },
                    fail: function(err) {
                        uni.hideLoading();
                        console.error('uni.login 接口调用失败', err);
                        uni.showToast({
                            title: '调用微信登录接口失败',
                            icon: 'none'
                        });
                    }
                });
            },

            // ... existing methods ...
		}
	}
</script>

<style lang="scss">
	/* ... existing styles ... */
    .login-bg {
        height: 100vh;
        width: 100vw;
        background-color: #f2f2f2;
        background-image: linear-gradient(to top, #a8edea 0%, #fed6e3 100%);
        background-size: cover;
        box-sizing: border-box;
        position: fixed;
        z-index: -1;
    }
    .userInfo {
        padding-top: 30%;
    }

    .userInfo .userPic {
        display: block;
        margin: 0 auto;
        border-radius: 50%;
        width: 300upx;
        height: 300upx;
    }
    // .wxLogo {
    //     width: 2rem;
    //     height: 2rem;
    //     vertical-align: middle;
    // }
    .userName {
        margin-top: 2rem;
        margin-bottom: 2rem;
        line-height: 2rem;
        text-align: center;
        color: lightskyblue;
        font-size: 2rem;
        font-family: Verdana, Geneva, Tahoma, sans-serif;
    }
    button {
        margin-bottom: 2rem;
    }

	/* 可以为微信登录按钮添加一些样式 */
	.wxLogo {
		width: 40rpx;
		height: 40rpx;
		vertical-align: middle;
		margin-right: 10rpx;
	}
	.margin-top {
		margin-top: 20rpx; /* 与上方按钮保持间距 */
	}
</style>