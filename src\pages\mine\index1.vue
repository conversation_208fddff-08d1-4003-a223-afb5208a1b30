<template>
    <view class="set-box">
      <uni-list :border="false">
        <!-- <uni-list-item title="账号与安全" to="/user/account" :border="false" showArrow/> -->
        <view style="height: 24rpx;background-color: #f9f9f9;"></view>
        <uni-list-item title="关于品客聚精彩" to="/user/about" :border="false" showArrow />
        <view style="height: 24rpx;background-color: #f9f9f9;"></view>
        <uni-list-item title="意见反馈" to="/science/feedback?type=1" :border="false" showArrow />
        <view style="height: 24rpx;background-color: #f9f9f9;"></view>
        <view style="height: 24rpx;background-color: #f9f9f9;"></view>
      </uni-list>
      <view class="logout">
        <button type="default" @click="logout" style="font-size: 14px;">退出登录</button>
      </view>
    </view>
  </template>
  
  <script>
//   import { useStore } from '@/store'
//   const store = useStore()
//   const logout = () => {
//     uni.showModal({
//       title: '确定退出？',
//       content: '',
//       success: function (res) {
//         if (res.confirm) {
//           store.commit('setProfile', '')
//           store.commit('setToken', '')
//           uni.clearStorageSync()
//           uni.switchTab({
//             url: '/pages/eventsList'
//           })
//         }
//       }
//     })
//   }
  </script>
  
  <style scoped lang="scss">
  .set-box{
    height: 100vh;
    background-color: #f9f9f9;
    padding-top: 24rpx;
    position: relative;
  .logout{
      position: absolute;
      width: 100vw;
      padding: 0 60rpx;
      bottom: 80px;
      left: 0;
    }
  }
  .position-my{
    display: flex;
    justify-content: space-between;
    padding: 6px 10px 6px 15px;
  }
  </style>